#
#
#   Licensed to the Apache Software Foundation (ASF) under one or more
#   contributor license agreements.  See the NOTICE file distributed with
#   this work for additional information regarding copyright ownership.
#   The ASF licenses this file to You under the Apache License, Version 2.0
#   (the "License"); you may not use this file except in compliance with
#   the License.  You may obtain a copy of the License at
#
#       http://www.apache.org/licenses/LICENSE-2.0
#
#   Unless required by applicable law or agreed to in writing, software
#   distributed under the License is distributed on an "AS IS" BASIS,
#   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#   See the License for the specific language governing permissions and
#   limitations under the License.
#
#

# application level, applies to all services
---
scope: application
force: true
runtime: false
enabled: true
priority: 1
key: demo-consumer
conditions:
- method!=sayHello => address=***********:20880
- method=routeMethod1 =>
...

# application level, only applies to a specific service
---
scope: application
force: true
runtime: false
enabled: true
priority: 1
key: demo-consumer
conditions:
- interface=org.apache.dubbo.demo.DemoService&method!=sayHello => host=***********
- interface=org.apache.dubbo.demo.DemoService&method=routeMethod1 => address=***********:20880
...

---
scope: service
force: true
runtime: false
enabled: true
priority: 1
key: org.apache.dubbo.demo.DemoService
conditions:
- method!=sayHello =>
- method=routeMethod1 => address=***********:20880
...