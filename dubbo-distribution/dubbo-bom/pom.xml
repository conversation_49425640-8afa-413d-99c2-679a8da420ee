<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.dubbo</groupId>
    <artifactId>dubbo-parent</artifactId>
    <version>${revision}</version>
    <relativePath>../../pom.xml</relativePath>
  </parent>

  <artifactId>dubbo-bom</artifactId>
  <packaging>pom</packaging>

  <name>dubbo-bom</name>

  <dependencyManagement>
    <dependencies>
      <!-- cluster -->
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-cluster</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- common -->
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-common</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- compatible -->
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-compatible</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- compiler -->
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-compiler</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- config -->
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-config</artifactId>
        <version>${project.version}</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-config-api</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-config-spring</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-config-spring6</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- config-center -->
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-configcenter</artifactId>
        <version>${project.version}</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-configcenter-file</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-configcenter-zookeeper</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-configcenter-apollo</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-configcenter-nacos</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- dependencies-bom -->
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-dependencies-bom</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-bom</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-all-shaded</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- filter -->
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-filter</artifactId>
        <version>${project.version}</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-filter-cache</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-filter-validation</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- metadata -->
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-metadata</artifactId>
        <version>${project.version}</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-metadata-api</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-metadata-rest</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-metadata-report-zookeeper</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-metadata-report-nacos</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-metadata-processor</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-metadata-definition-protobuf</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- metrics -->
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-metrics</artifactId>
        <version>${project.version}</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-metrics-api</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-metrics-event</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-metrics-default</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-metrics-registry</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-metrics-prometheus</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-metrics-metadata</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-metrics-config-center</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-metrics-netty</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- tracing -->
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-tracing</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- native -->
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-native</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-maven-plugin</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- plugin -->
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-auth</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-security</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-qos-api</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-qos</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-reactive</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-spring-security</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-rest-jaxrs</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-rest-spring</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-rest-openapi</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-triple-servlet</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-triple-websocket</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- registry -->
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-registry</artifactId>
        <version>${project.version}</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-registry-api</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-registry-multicast</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-registry-multiple</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-registry-nacos</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-registry-zookeeper</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-xds</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- remoting -->
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-remoting</artifactId>
        <version>${project.version}</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-remoting-api</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-remoting-http12</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-remoting-http3</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-remoting-websocket</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-remoting-netty</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-remoting-netty4</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-remoting-zookeeper-curator5</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- rpc -->
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-rpc</artifactId>
        <version>${project.version}</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-rpc-api</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-rpc-dubbo</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-rpc-injvm</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-rpc-triple</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- serialization -->
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-serialization</artifactId>
        <version>${project.version}</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-serialization-api</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-serialization-hessian2</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-serialization-fastjson2</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- spring-boot -->
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-spring-boot</artifactId>
        <version>${project.version}</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-spring-boot-actuator</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-spring-boot-actuator-autoconfigure</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-spring-boot-autoconfigure</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-spring-boot-3-autoconfigure</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-spring-boot-compatible</artifactId>
        <version>${project.version}</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-spring-boot-autoconfigure-compatible</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-spring-boot-actuator-autoconfigure-compatible</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-spring-boot-starter</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-spring-boot</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-spring-boot-starters</artifactId>
        <version>${project.version}</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-tracing-otel-zipkin-spring-boot-starter</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-tracing-otel-otlp-spring-boot-starter</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-tracing-brave-zipkin-spring-boot-starter</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-observability-spring-boot-starter</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-nacos-spring-boot-starter</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-zookeeper-curator5-spring-boot-starter</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-sentinel-spring-boot-starter</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-seata-spring-boot-starter</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- test -->
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-test</artifactId>
        <version>${project.version}</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-test-check</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-test-common</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-test-modules</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-test-spring</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-test-spring3.2</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-test-spring4.1</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-test-spring4.2</artifactId>
        <version>${project.version}</version>
      </dependency>

    </dependencies>
  </dependencyManagement>

  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>flatten-maven-plugin</artifactId>
        <version>${maven_flatten_version}</version>
        <configuration>
          <updatePomFile>true</updatePomFile>
          <flattenMode>bom</flattenMode>
          <pomElements>
            <dependencyManagement>expand</dependencyManagement>
            <properties>remove</properties>
            <repositories>remove</repositories>
            <profiles>remove</profiles>
          </pomElements>
        </configuration>
        <executions>
          <execution>
            <id>flatten</id>
            <goals>
              <goal>flatten</goal>
            </goals>
            <phase>process-resources</phase>
          </execution>
          <execution>
            <id>flatten.clean</id>
            <goals>
              <goal>clean</goal>
            </goals>
            <phase>clean</phase>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>release</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <executions>
              <execution>
                <goals>
                  <goal>sign</goal>
                </goals>
                <phase>verify</phase>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

</project>
