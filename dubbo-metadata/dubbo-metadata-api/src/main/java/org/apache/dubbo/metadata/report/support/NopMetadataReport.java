/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.dubbo.metadata.report.support;

import org.apache.dubbo.common.URL;
import org.apache.dubbo.metadata.definition.model.ServiceDefinition;
import org.apache.dubbo.metadata.report.MetadataReport;
import org.apache.dubbo.metadata.report.identifier.MetadataIdentifier;
import org.apache.dubbo.metadata.report.identifier.ServiceMetadataIdentifier;
import org.apache.dubbo.metadata.report.identifier.SubscriberMetadataIdentifier;

import java.util.List;
import java.util.Map;
import java.util.Set;

public class NopMetadataReport implements MetadataReport {
    public NopMetadataReport() {}

    @Override
    public void storeProviderMetadata(
            MetadataIdentifier providerMetadataIdentifier, ServiceDefinition serviceDefinition) {}

    @Override
    public String getServiceDefinition(MetadataIdentifier metadataIdentifier) {
        return null;
    }

    @Override
    public void storeConsumerMetadata(
            MetadataIdentifier consumerMetadataIdentifier, Map<String, String> serviceParameterMap) {}

    @Override
    public List<String> getExportedURLs(ServiceMetadataIdentifier metadataIdentifier) {
        return null;
    }

    @Override
    public void destroy() {}

    @Override
    public void saveServiceMetadata(ServiceMetadataIdentifier metadataIdentifier, URL url) {}

    @Override
    public void removeServiceMetadata(ServiceMetadataIdentifier metadataIdentifier) {}

    @Override
    public void saveSubscribedData(SubscriberMetadataIdentifier subscriberMetadataIdentifier, Set<String> urls) {}

    @Override
    public List<String> getSubscribedURLs(SubscriberMetadataIdentifier subscriberMetadataIdentifier) {
        return null;
    }

    @Override
    public boolean shouldReportDefinition() {
        return true;
    }

    @Override
    public boolean shouldReportMetadata() {
        return false;
    }
}
