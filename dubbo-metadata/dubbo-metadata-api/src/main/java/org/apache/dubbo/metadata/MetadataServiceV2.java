/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.dubbo.metadata;

import java.util.concurrent.CompletableFuture;

public interface MetadataServiceV2 extends org.apache.dubbo.rpc.model.DubboStub {

    String JAVA_SERVICE_NAME = "org.apache.dubbo.metadata.MetadataServiceV2";
    String SERVICE_NAME = "org.apache.dubbo.metadata.MetadataServiceV2";

    /**
     * <pre>
     *  Retrieves metadata information.
     * </pre>
     */
    MetadataInfoV2 getMetadataInfo(MetadataRequest request);

    CompletableFuture<MetadataInfoV2> getMetadataInfoAsync(MetadataRequest request);

    /**
     * <pre>
     *  Retrieves OpenAPI information.
     * </pre>
     */
    OpenAPIInfo getOpenAPIInfo(OpenAPIRequest request);

    CompletableFuture<OpenAPIInfo> getOpenAPIInfoAsync(OpenAPIRequest request);
}
