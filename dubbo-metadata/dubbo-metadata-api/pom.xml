<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.apache.dubbo</groupId>
    <artifactId>dubbo-metadata</artifactId>
    <version>${revision}</version>
    <relativePath>../pom.xml</relativePath>
  </parent>

  <artifactId>dubbo-metadata-api</artifactId>
  <packaging>jar</packaging>

  <name>dubbo-metadata-api</name>
  <description>The metadata module of Dubbo project</description>

  <dependencies>
    <dependency>
      <groupId>org.apache.dubbo</groupId>
      <artifactId>dubbo-rpc-api</artifactId>
      <version>${project.parent.version}</version>
      <optional>true</optional>
    </dependency>

    <dependency>
      <groupId>org.apache.dubbo</groupId>
      <artifactId>dubbo-cluster</artifactId>
      <version>${project.parent.version}</version>
    </dependency>

    <dependency>
      <groupId>org.apache.dubbo</groupId>
      <artifactId>dubbo-metrics-api</artifactId>
      <version>${project.parent.version}</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.dubbo</groupId>
      <artifactId>dubbo-metrics-default</artifactId>
      <version>${project.parent.version}</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.dubbo</groupId>
      <artifactId>dubbo-metrics-metadata</artifactId>
      <version>${project.parent.version}</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java</artifactId>
    </dependency>

    <dependency>
      <groupId>org.apache.dubbo</groupId>
      <artifactId>dubbo-native</artifactId>
      <version>${project.parent.version}</version>
    </dependency>

    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-slf4j-impl</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.apache.dubbo</groupId>
      <artifactId>dubbo-rpc-triple</artifactId>
      <version>${project.parent.version}</version>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <!--      <plugin>
              <groupId>org.apache.dubbo</groupId>
              <artifactId>dubbo-maven-plugin</artifactId>
            </plugin>-->
    </plugins>
  </build>
</project>
