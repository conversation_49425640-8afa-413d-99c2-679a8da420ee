application.prefix = dubbo.application.
application.prefixes = dubbo.applications.
# single bean definition

## application
dubbo.application.id = applicationBean
dubbo.application.name = dubbo-demo-application

## module
dubbo.module.id = moduleBean
dubbo.module.name = dubbo-demo-module

## registry
dubbo.registry.address = zookeeper://**************:32770
dubbo.registry.useAsConfigCenter = false
dubbo.registry.useAsMetadataCenter = false

## protocol
dubbo.protocol.name = dubbo
dubbo.protocol.port = 20880

dubbo.protocols.rest.port=8080
dubbo.protocols.thrift.port=9090

## monitor
dubbo.monitor.address = zookeeper://127.0.0.1:32770

## provider
dubbo.provider.host = 127.0.0.1

## consumer
dubbo.consumer.client = netty

# multiple Bean definition
dubbo.registries.registry1.address = zookeeper://localhost:2181
dubbo.registries.registry2.address = zookeeper://localhost:2182
