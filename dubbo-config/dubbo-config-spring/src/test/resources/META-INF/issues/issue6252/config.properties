dubbo.application.name=demo-zk
dubbo.application.qos-enable=false
dubbo.protocol.name=dubbo
dubbo.protocol.port=-1
dubbo.scan.basePackages=com.example.demo
dubbo.consumer.check=false
dubbo.registries.z214.address=${zookeeper.connection.address.1}
dubbo.registries.z214.timeout=20000
dubbo.registries.z214.subscribe=false
dubbo.registries.z214.useAsConfigCenter=false
dubbo.registries.z214.useAsMetadataCenter=false
dubbo.registries.z205.address=${zookeeper.connection.address.2}
dubbo.registries.z205.timeout=20000
dubbo.registries.z205.useAsConfigCenter=false
dubbo.registries.z205.useAsMetadataCenter=false
