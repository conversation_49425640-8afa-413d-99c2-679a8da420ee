# Dubbo Consumer for Zookeeper

dubbo.application.name = zookeeper-dubbo-spring-consumer

dubbo.registry.address = ${zookeeper.connection.address}?registry-type=service
dubbo.registry.useAsConfigCenter = true
dubbo.registry.useAsMetadataCenter = true

dubbo.protocol.name = dubbo
dubbo.protocol.port = -1

dubbo.provider.name = zookeeper-dubbo-spring-provider
dubbo.provider.name1 = zookeeper-dubbo-spring-provider-1
dubbo.provider.name2 = zookeeper-dubbo-spring-provider-2
