<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://dubbo.apache.org/schema/dubbo"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.3.xsd
       http://dubbo.apache.org/schema/dubbo http://dubbo.apache.org/schema/dubbo/dubbo.xsd">

    <!-- NOTE: we need config executor-management-mode="isolation" -->
    <dubbo:application name="demo-provider" executor-management-mode="isolation">
    </dubbo:application>

    <dubbo:config-center address="zookeeper://127.0.0.1:2181"/>
    <dubbo:metadata-report address="zookeeper://127.0.0.1:2181"/>
    <dubbo:registry id="registry1" address="zookeeper://127.0.0.1:2181"/>

    <dubbo:protocol name="dubbo" port="-1"/>
    <dubbo:protocol name="tri" port="-1"/>

    <!-- expose three service with dubbo and tri protocol-->
    <bean id="demoServiceV1" class="org.apache.dubbo.config.spring.impl.DemoServiceImpl"/>
    <bean id="helloServiceV2" class="org.apache.dubbo.config.spring.impl.HelloServiceImpl"/>
    <bean id="helloServiceV3" class="org.apache.dubbo.config.spring.impl.HelloServiceImpl"/>

    <!-- customized thread pool -->
    <bean id="executor-demo-service"
          class="org.apache.dubbo.config.spring.isolation.spring.support.DemoServiceExecutor"/>
    <bean id="executor-hello-service"
          class="org.apache.dubbo.config.spring.isolation.spring.support.HelloServiceExecutor"/>

    <!-- this service use [executor="executor-demo-service"] as isolated thread pool-->
    <dubbo:service executor="executor-demo-service"
                   interface="org.apache.dubbo.config.spring.api.DemoService" version="1.0.0" group="Group1"
                   timeout="3000" ref="demoServiceV1" registry="registry1" protocol="dubbo,tri"/>

    <!-- this service use [executor="executor-hello-service"] as isolated thread pool-->
    <dubbo:service executor="executor-hello-service"
                   interface="org.apache.dubbo.config.spring.api.HelloService" version="2.0.0" group="Group2"
                   timeout="5000" ref="helloServiceV2" registry="registry1" protocol="dubbo,tri"/>

    <!-- not set executor for this service, the default executor built using threadpool parameter of the protocolConfig -->
    <dubbo:service interface="org.apache.dubbo.config.spring.api.HelloService" version="3.0.0" group="Group3"
                   timeout="5000" ref="helloServiceV3" registry="registry1" protocol="dubbo,tri"/>

</beans>
