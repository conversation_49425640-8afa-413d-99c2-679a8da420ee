<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://dubbo.apache.org/schema/dubbo"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.3.xsd
       http://dubbo.apache.org/schema/dubbo http://dubbo.apache.org/schema/dubbo/dubbo.xsd">

    <dubbo:application name="demo-consumer">
    </dubbo:application>

    <dubbo:metadata-report address="zookeeper://127.0.0.1:2181"/>

    <dubbo:registry id="demo1" address="zookeeper://127.0.0.1:2181"/>

    <!-- refer with dubbo protocol-->
    <dubbo:reference version="1.0.0" group="Group1" id="dubbo-demoServiceV1" check="false" scope="remote"
                     interface="org.apache.dubbo.config.spring.api.DemoService" protocol="dubbo"/>

    <dubbo:reference version="2.0.0" group="Group2" id="dubbo-helloServiceV2" check="false" scope="remote"
                     interface="org.apache.dubbo.config.spring.api.HelloService" protocol="dubbo"/>

    <dubbo:reference version="3.0.0" group="Group3" id="dubbo-helloServiceV3" check="false" scope="remote"
                     interface="org.apache.dubbo.config.spring.api.HelloService" protocol="dubbo"/>

    <!-- refer with tri protocol-->
    <dubbo:reference version="1.0.0" group="Group1" id="tri-demoServiceV1" check="false" scope="remote"
                     interface="org.apache.dubbo.config.spring.api.DemoService" protocol="tri"/>

    <dubbo:reference version="2.0.0" group="Group2" id="tri-helloServiceV2" check="false" scope="remote"
                     interface="org.apache.dubbo.config.spring.api.HelloService" protocol="tri"/>

    <dubbo:reference version="3.0.0" group="Group3" id="tri-helloServiceV3" check="false" scope="remote"
                     interface="org.apache.dubbo.config.spring.api.HelloService" protocol="tri"/>

</beans>
