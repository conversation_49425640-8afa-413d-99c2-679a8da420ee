# Dubbo Consumer Properties as an alternative for
# Spring XML Bean definition : META-INF/spring/dubbo-annotation-consumer.xml
demo.service.application = dubbo-annotation-test
demo.service.registry = my-registry

## Dubbo configs binding properties
###  <dubbo:application name="dubbo-demo-application"/>
# In this UT, the provider will be responsible of loading ApplicationConfig.
dubbo.applications.dubbo-demo-application.name = dubbo-demo-application

### <dubbo:registry id="my-registry" address="N/A"/>
dubbo.registries.my-registry.address = N/A
dubbo.registries.my-registry2.address = N/A
