<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://dubbo.apache.org/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans        http://www.springframework.org/schema/beans/spring-beans-4.3.xsd        http://dubbo.apache.org/schema/dubbo        http://dubbo.apache.org/schema/dubbo/dubbo.xsd">

    <dubbo:application name="dubbo-consumer-xml-demo"/>

    <!-- Nacos Registry -->
    <dubbo:registry address="nacos://127.0.0.1:8848"/>

    <!-- Reference interface -->
    <dubbo:reference id="demoService1" interface="org.apache.dubbo.config.spring.api.DemoService" version="2.0.0"
                     group="default"/>

    <dubbo:reference id="demoService2" interface="org.apache.dubbo.config.spring.api.DemoService" version="*"
                     group="default"/>

    <dubbo:reference id="demoService3" interface="org.apache.dubbo.config.spring.api.DemoService" version="2.0.0"
                     group="default"/>

    <dubbo:reference id="demoService4" interface="org.apache.dubbo.config.spring.api.DemoService" version="2.0.0" group="*"/>

    <dubbo:reference id="demoService5" interface="org.apache.dubbo.config.spring.api.DemoService" version="2.0.0"
                     group="default,test"/>

</beans>
