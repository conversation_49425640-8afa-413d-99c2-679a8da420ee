<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://dubbo.apache.org/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.3.xsd        http://dubbo.apache.org/schema/dubbo        http://dubbo.apache.org/schema/dubbo/dubbo.xsd">

    <dubbo:application name="dubbo-provider-xml-demo"/>

    <!-- Nacos Registry -->
    <dubbo:registry address="nacos://127.0.0.1:8848"/>

    <!-- Use random port as Dubbo -->
    <dubbo:protocol name="dubbo" port="-1"/>

    <dubbo:service interface="org.apache.dubbo.config.spring.api.DemoService" ref="demoService" version="2.0.0"
                   group="default"/>

    <bean id="demoService" class="org.apache.dubbo.config.spring.registry.nacos.demo.service.DefaultService"/>
</beans>
