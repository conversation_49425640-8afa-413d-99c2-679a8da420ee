<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:dubbo="http://dubbo.apache.org/schema/dubbo"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.3.xsd
    http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.3.xsd
    http://dubbo.apache.org/schema/dubbo http://dubbo.apache.org/schema/dubbo/dubbo.xsd"
       default-autowire="byName">

    <context:annotation-config/>
    <bean id="demoInterceptor" class="org.apache.dubbo.config.spring.action.DemoInterceptor"/>
    <bean id="demoAdvisor" class="org.springframework.aop.support.RegexpMethodPointcutAdvisor">
        <property name="advice" ref="demoInterceptor"/>
        <property name="patterns">
            <list>
                <value>.*</value>
            </list>
        </property>
    </bean>
    <bean id="demoProxyCreator" class="org.springframework.aop.framework.autoproxy.BeanNameAutoProxyCreator">
        <property name="beanNames">
            <list>
                <value>demoService</value>
            </list>
        </property>
        <property name="interceptorNames">
            <list>
                <value>demoAdvisor</value>
            </list>
        </property>
    </bean>

    <!-- 当前应用信息配置 -->
    <dubbo:application name="consumer"/>

    <!-- 连接注册中心配置 -->
    <dubbo:registry address="N/A"/>

    <!-- 引用服务配置 -->
    <dubbo:reference id="demoService" interface="org.apache.dubbo.config.spring.api.DemoService"
                     url="dubbo://127.0.0.1:20813"/>

    <bean id="demoActionBySetter" class="org.apache.dubbo.config.spring.action.DemoActionBySetter"/>

    <bean id="demoActionByAnnotation" class="org.apache.dubbo.config.spring.action.DemoActionByAnnotation"/>

</beans>