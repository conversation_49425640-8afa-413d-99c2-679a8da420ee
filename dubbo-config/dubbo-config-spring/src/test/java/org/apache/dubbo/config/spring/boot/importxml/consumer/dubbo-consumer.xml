<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~
  ~   Licensed to the Apache Software Foundation (ASF) under one or more
  ~   contributor license agreements.  See the NOTICE file distributed with
  ~   this work for additional information regarding copyright ownership.
  ~   The ASF licenses this file to You under the Apache License, Version 2.0
  ~   (the "License"); you may not use this file except in compliance with
  ~   the License.  You may obtain a copy of the License at
  ~
  ~       http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~   Unless required by applicable law or agreed to in writing, software
  ~   distributed under the License is distributed on an "AS IS" BASIS,
  ~   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~   See the License for the specific language governing permissions and
  ~   limitations under the License.
  ~
  -->
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
	xsi:schemaLocation="http://www.springframework.org/schema/beans 
		http://www.springframework.org/schema/beans/spring-beans.xsd
		http://code.alibabatech.com/schema/dubbo
		http://code.alibabatech.com/schema/dubbo/dubbo.xsd" default-lazy-init ="false">

	<description>wisdom app Service Dubbo Admin Consumers</description>
	<dubbo:application name="cimmdms"
		owner="wisdom" organization="wisdom" logger="slf4j">
		<dubbo:parameter key="qos.enable" value="false" />
	</dubbo:application>

	<dubbo:protocol name="dubbo" accesslog="true" />
	
	<dubbo:registry id="cimmdmsSrvClientRegistry"
		protocol="${dubbo.registry.protocol}" address="${dubbo.registry.address}" client="curator"
		group="dubboservice/wisdom/cimmdmsservice/group_mdms_dev"
		subscribe="true" check="true">
	</dubbo:registry>

	<dubbo:consumer id="wisdomcimmdmsSrvConsumer"
		registry="cimmdmsSrvClientRegistry" init="false" check="false"
		retries="0" timeout="25000">
		<dubbo:reference consumer="wisdomcimmdmsSrvConsumer" interface="org.apache.dubbo.config.spring.api.HelloService" id="mdmMessageProviderService" />
	</dubbo:consumer>
</beans>
