<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://dubbo.apache.org/schema/dubbo"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.3.xsd
       http://dubbo.apache.org/schema/dubbo http://dubbo.apache.org/schema/dubbo/dubbo.xsd">

    <bean id="propertyConfigurer" class="org.springframework.context.support.PropertySourcesPlaceholderConfigurer">
        <property name="location">
            <value>classpath:/org/apache/dubbo/config/spring/propertyconfigurer/consumer2/app.properties</value>
        </property>
    </bean>

    <dubbo:application name="demo-consumer">
    </dubbo:application>

    <!--    <dubbo:metadata-report address="${zookeeper.connection.address}"/>-->

    <dubbo:registry address="${dubbo.registry.address}"/>

    <!--  timeout="${dubbo.call-timeout}"  -->
    <dubbo:reference id="demoService" check="false" group="${biz.group}" timeout="${dubbo.call-timeout:foo-timeout}"
                     interface="org.apache.dubbo.config.spring.api.HelloService"/>


</beans>
