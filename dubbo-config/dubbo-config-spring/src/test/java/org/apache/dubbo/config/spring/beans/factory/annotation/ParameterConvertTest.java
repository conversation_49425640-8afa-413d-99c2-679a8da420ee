/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.dubbo.config.spring.beans.factory.annotation;

import org.apache.dubbo.config.spring.util.DubboAnnotationUtils;

import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.test.annotation.DirtiesContext;

/**
 * {@link DubboAnnotationUtils#convertParameters} Test
 */
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
class ParameterConvertTest {

    @Test
    void test() {
        /**
         *     (array->map)
         *     ["a","b"] ==> {a=b}
         *     [" a "," b "] ==> {a=b}
         *     ["a=b"] ==>{a=b}
         *     ["a:b"] ==>{a=b}
         *     ["a=b","c","d"] ==>{a=b,c=d}
         *     ["a=b","c:d"] ==>{a=b,c=d}
         *     ["a","a:b"] ==>{a=a:b}
         */
        Map<String, String> parametersMap = new HashMap<>();
        parametersMap.put("a", "b");
        Assertions.assertEquals(parametersMap, DubboAnnotationUtils.convertParameters(new String[] {"a", "b"}));
        Assertions.assertEquals(parametersMap, DubboAnnotationUtils.convertParameters(new String[] {" a ", " b "}));
        Assertions.assertEquals(parametersMap, DubboAnnotationUtils.convertParameters(new String[] {"a=b"}));
        Assertions.assertEquals(parametersMap, DubboAnnotationUtils.convertParameters(new String[] {"a:b"}));

        parametersMap.put("c", "d");
        Assertions.assertEquals(parametersMap, DubboAnnotationUtils.convertParameters(new String[] {"a=b", "c", "d"}));
        Assertions.assertEquals(parametersMap, DubboAnnotationUtils.convertParameters(new String[] {"a:b", "c=d"}));

        parametersMap.clear();
        parametersMap.put("a", "a:b");
        Assertions.assertEquals(parametersMap, DubboAnnotationUtils.convertParameters(new String[] {"a", "a:b"}));

        parametersMap.clear();
        parametersMap.put("a", "0,100");
        Assertions.assertEquals(parametersMap, DubboAnnotationUtils.convertParameters(new String[] {"a", "0,100"}));
    }
}
