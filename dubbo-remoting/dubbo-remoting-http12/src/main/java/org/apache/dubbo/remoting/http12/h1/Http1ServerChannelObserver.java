/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.dubbo.remoting.http12.h1;

import org.apache.dubbo.remoting.http12.AbstractServerHttpChannelObserver;
import org.apache.dubbo.remoting.http12.HttpChannel;
import org.apache.dubbo.remoting.http12.HttpMetadata;
import org.apache.dubbo.remoting.http12.HttpOutputMessage;
import org.apache.dubbo.remoting.http12.netty4.h1.NettyHttp1HttpHeaders;

public class Http1ServerChannelObserver extends AbstractServerHttpChannelObserver<HttpChannel> {

    public Http1ServerChannelObserver(HttpChannel httpChannel) {
        super(httpChannel);
    }

    @Override
    protected HttpMetadata encodeHttpMetadata(boolean endStream) {
        return new Http1Metadata(new NettyHttp1HttpHeaders());
    }

    @Override
    protected void doOnCompleted(Throwable throwable) {
        getHttpChannel().writeMessage(HttpOutputMessage.EMPTY_MESSAGE);
    }
}
