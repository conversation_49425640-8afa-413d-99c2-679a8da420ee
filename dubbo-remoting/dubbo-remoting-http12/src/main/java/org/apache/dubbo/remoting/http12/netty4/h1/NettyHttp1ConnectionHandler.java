/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.dubbo.remoting.http12.netty4.h1;

import org.apache.dubbo.common.URL;
import org.apache.dubbo.config.nested.TripleConfig;
import org.apache.dubbo.remoting.http12.h1.Http1Request;
import org.apache.dubbo.remoting.http12.h1.Http1ServerTransportListener;
import org.apache.dubbo.remoting.http12.h1.Http1ServerTransportListenerFactory;
import org.apache.dubbo.rpc.model.FrameworkModel;

import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;

public class NettyHttp1ConnectionHandler extends SimpleChannelInboundHandler<Http1Request> {

    private final URL url;

    private final FrameworkModel frameworkModel;

    private final Http1ServerTransportListenerFactory http1ServerTransportListenerFactory;

    private final TripleConfig tripleConfig;

    public NettyHttp1ConnectionHandler(
            URL url,
            FrameworkModel frameworkModel,
            TripleConfig tripleConfig,
            Http1ServerTransportListenerFactory http1ServerTransportListenerFactory) {
        this.url = url;
        this.frameworkModel = frameworkModel;
        this.tripleConfig = tripleConfig;
        this.http1ServerTransportListenerFactory = http1ServerTransportListenerFactory;
    }

    /**
     * process h1 request
     */
    protected void channelRead0(ChannelHandlerContext ctx, Http1Request http1Request) {
        Http1ServerTransportListener http1TransportListener = http1ServerTransportListenerFactory.newInstance(
                new NettyHttp1Channel(ctx.channel(), tripleConfig), url, frameworkModel);
        http1TransportListener.onMetadata(http1Request);
        http1TransportListener.onData(http1Request);
    }
}
