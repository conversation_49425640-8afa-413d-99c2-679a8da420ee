/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.dubbo.remoting.websocket.netty4;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.http.FullHttpRequest;
import io.netty.handler.codec.http.HttpHeaders;
import io.netty.handler.codec.http.HttpServerUpgradeHandler;

public class WebSocketServerUpgradeCodec implements HttpServerUpgradeHandler.UpgradeCodec {

    private final List<Class<? extends ChannelHandler>> shouldRemoveChannelHandlers;

    private final ChannelHandler[] channelHandlers;

    public WebSocketServerUpgradeCodec(
            List<Class<? extends ChannelHandler>> shouldRemoveChannelHandlers, ChannelHandler... channelHandlers) {
        this.shouldRemoveChannelHandlers = shouldRemoveChannelHandlers;
        this.channelHandlers = channelHandlers;
    }

    @Override
    public Collection<CharSequence> requiredUpgradeHeaders() {
        return Collections.emptyList();
    }

    @Override
    public boolean prepareUpgradeResponse(
            ChannelHandlerContext ctx, FullHttpRequest upgradeRequest, HttpHeaders upgradeHeaders) {
        if (shouldRemoveChannelHandlers != null) {
            for (Class<? extends ChannelHandler> shouldRemoveChannelHandler : shouldRemoveChannelHandlers) {
                ctx.pipeline().remove(shouldRemoveChannelHandler);
            }
        }
        if (channelHandlers != null) {
            for (ChannelHandler channelHandler : channelHandlers) {
                ctx.pipeline().addLast(channelHandler);
            }
        }
        return false;
    }

    @Override
    public void upgradeTo(ChannelHandlerContext ctx, FullHttpRequest upgradeRequest) {}
}
