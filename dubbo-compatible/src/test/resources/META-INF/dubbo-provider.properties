# Dubbo Provider Properties as an alternative for
# Spring XML Bean definition : META-INF/spring/dubbo-annotation-provider.xml

## Service Providers' Placeholders for org.apache.dubbo.config.spring.context.annotation.provider.DemoServiceImpl

demo.service.application = dubbo-demo-application
demo.service.protocol = dubbo
demo.service.registry = my-registry


## Dubbo configs binding properties

### <dubbo:application name="dubbo-demo-application"/>
dubbo.application.id = dubbo-demo-application
dubbo.application.name = dubbo-demo-application

### <dubbo:registry id="my-registry" address="N/A"/>
dubbo.registry.id = my-registry
dubbo.registry.address = N/A

### <dubbo:protocol name="dubbo" port="12345"/>
dubbo.protocol.name = dubbo
dubbo.protocol.port = 12345
dubbo.monitor.address=N/A
