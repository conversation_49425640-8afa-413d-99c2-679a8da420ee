# Dubbo Consumer Properties as an alternative for
# Spring XML Bean definition : META-INF/spring/dubbo-annotation-consumer.xml
demo.service.application = dubbo-demo-application
demo.service.registry = my-registry

## Dubbo configs binding properties
###  <dubbo:application name="dubbo-demo-application"/>
dubbo.applications.dubbo-demo-application.name = dubbo-demo-application

### <dubbo:registry id="my-registry" address="N/A"/>
dubbo.registries.my-registry.address = N/A
dubbo.registries.my-registry2.address = N/A
