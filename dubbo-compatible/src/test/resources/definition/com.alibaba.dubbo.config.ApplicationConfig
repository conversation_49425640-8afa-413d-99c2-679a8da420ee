//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

public void com.alibaba.dubbo.config.ApplicationConfig.setVersion(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ApplicationConfig.getOrganization()
public void com.alibaba.dubbo.config.ApplicationConfig.setOrganization(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ApplicationConfig.getArchitecture()
public void com.alibaba.dubbo.config.ApplicationConfig.setArchitecture(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ApplicationConfig.getEnvironment()
public void com.alibaba.dubbo.config.ApplicationConfig.setEnvironment(java.lang.String)
public com.alibaba.dubbo.config.RegistryConfig com.alibaba.dubbo.config.ApplicationConfig.getRegistry()
public void com.alibaba.dubbo.config.ApplicationConfig.setRegistry(com.alibaba.dubbo.config.RegistryConfig)
public java.util.List com.alibaba.dubbo.config.ApplicationConfig.getRegistries()
public void com.alibaba.dubbo.config.ApplicationConfig.setRegistries(java.util.List)
public com.alibaba.dubbo.config.MonitorConfig com.alibaba.dubbo.config.ApplicationConfig.getMonitor()
public void com.alibaba.dubbo.config.ApplicationConfig.setMonitor(java.lang.String)
public void com.alibaba.dubbo.config.ApplicationConfig.setMonitor(com.alibaba.dubbo.config.MonitorConfig)
public java.lang.String com.alibaba.dubbo.config.ApplicationConfig.getCompiler()
public void com.alibaba.dubbo.config.ApplicationConfig.setCompiler(java.lang.String)
public void com.alibaba.dubbo.config.ApplicationConfig.setLogger(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ApplicationConfig.getDumpDirectory()
public void com.alibaba.dubbo.config.ApplicationConfig.setDumpDirectory(java.lang.String)
public java.lang.Boolean com.alibaba.dubbo.config.ApplicationConfig.getQosEnable()
public void com.alibaba.dubbo.config.ApplicationConfig.setQosEnable(java.lang.Boolean)
public java.lang.Integer com.alibaba.dubbo.config.ApplicationConfig.getQosPort()
public void com.alibaba.dubbo.config.ApplicationConfig.setQosPort(java.lang.Integer)
public java.lang.Boolean com.alibaba.dubbo.config.ApplicationConfig.getQosAcceptForeignIp()
public void com.alibaba.dubbo.config.ApplicationConfig.setQosAcceptForeignIp(java.lang.Boolean)
public java.lang.String com.alibaba.dubbo.config.ApplicationConfig.getName()
public java.lang.Boolean com.alibaba.dubbo.config.ApplicationConfig.isDefault()
public void com.alibaba.dubbo.config.ApplicationConfig.setName(java.lang.String)
public java.util.Map com.alibaba.dubbo.config.ApplicationConfig.getParameters()
public void com.alibaba.dubbo.config.ApplicationConfig.setDefault(java.lang.Boolean)
public java.lang.String com.alibaba.dubbo.config.ApplicationConfig.getLogger()
public java.lang.String com.alibaba.dubbo.config.ApplicationConfig.getOwner()
public void com.alibaba.dubbo.config.ApplicationConfig.setOwner(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ApplicationConfig.getVersion()
public void com.alibaba.dubbo.config.ApplicationConfig.setParameters(java.util.Map)
public void com.alibaba.dubbo.config.ApplicationConfig.setId(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ApplicationConfig.toString()
public java.lang.String com.alibaba.dubbo.config.ApplicationConfig.getId()
public final void com.alibaba.dubbo.config.ApplicationConfig.wait(long,int) throws java.lang.InterruptedException
public final native void com.alibaba.dubbo.config.ApplicationConfig.wait(long) throws java.lang.InterruptedException
public final void com.alibaba.dubbo.config.ApplicationConfig.wait() throws java.lang.InterruptedException
public boolean com.alibaba.dubbo.config.ApplicationConfig.equals(java.lang.Object)
public native int com.alibaba.dubbo.config.ApplicationConfig.hashCode()
public final native java.lang.Class com.alibaba.dubbo.config.ApplicationConfig.getClass()
public final native void com.alibaba.dubbo.config.ApplicationConfig.notify()
public final native void com.alibaba.dubbo.config.ApplicationConfig.notifyAll()
