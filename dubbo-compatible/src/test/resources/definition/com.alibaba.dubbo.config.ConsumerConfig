//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

public void com.alibaba.dubbo.config.ConsumerConfig.setTimeout(java.lang.Integer)
public java.lang.String com.alibaba.dubbo.config.ConsumerConfig.getClient()
public void com.alibaba.dubbo.config.ConsumerConfig.setClient(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ConsumerConfig.getThreadpool()
public void com.alibaba.dubbo.config.ConsumerConfig.setThreadpool(java.lang.String)
public java.lang.Integer com.alibaba.dubbo.config.ConsumerConfig.getCorethreads()
public void com.alibaba.dubbo.config.ConsumerConfig.setCorethreads(java.lang.Integer)
public void com.alibaba.dubbo.config.ConsumerConfig.setThreads(java.lang.Integer)
public java.lang.Integer com.alibaba.dubbo.config.ConsumerConfig.getQueues()
public void com.alibaba.dubbo.config.ConsumerConfig.setQueues(java.lang.Integer)
public java.lang.Boolean com.alibaba.dubbo.config.ConsumerConfig.getDefault()
public java.lang.Boolean com.alibaba.dubbo.config.ConsumerConfig.isDefault()
public java.lang.Integer com.alibaba.dubbo.config.ConsumerConfig.getThreads()
public void com.alibaba.dubbo.config.ConsumerConfig.setDefault(java.lang.Boolean)
public void com.alibaba.dubbo.config.ConsumerConfig.setVersion(java.lang.String)
public void com.alibaba.dubbo.config.ConsumerConfig.setGeneric(java.lang.Boolean)
public void com.alibaba.dubbo.config.ConsumerConfig.setGeneric(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ConsumerConfig.getGeneric()
public void com.alibaba.dubbo.config.ConsumerConfig.setListener(java.lang.String)
public void com.alibaba.dubbo.config.ConsumerConfig.setOnconnect(java.lang.String)
public void com.alibaba.dubbo.config.ConsumerConfig.setOndisconnect(java.lang.String)
public java.lang.Boolean com.alibaba.dubbo.config.ConsumerConfig.getStubevent()
public java.lang.String com.alibaba.dubbo.config.ConsumerConfig.getReconnect()
public void com.alibaba.dubbo.config.ConsumerConfig.setReconnect(java.lang.String)
public java.lang.Boolean com.alibaba.dubbo.config.ConsumerConfig.getSticky()
public void com.alibaba.dubbo.config.ConsumerConfig.setSticky(java.lang.Boolean)
public java.lang.Boolean com.alibaba.dubbo.config.ConsumerConfig.getLazy()
public void com.alibaba.dubbo.config.ConsumerConfig.setInit(java.lang.Boolean)
public void com.alibaba.dubbo.config.ConsumerConfig.setInjvm(java.lang.Boolean)
public void com.alibaba.dubbo.config.ConsumerConfig.setLazy(java.lang.Boolean)
public java.lang.Boolean com.alibaba.dubbo.config.ConsumerConfig.isInjvm()
public void com.alibaba.dubbo.config.ConsumerConfig.setCheck(java.lang.Boolean)
public java.lang.Boolean com.alibaba.dubbo.config.ConsumerConfig.isCheck()
public void com.alibaba.dubbo.config.ConsumerConfig.setGroup(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ConsumerConfig.getGroup()
public java.lang.Boolean com.alibaba.dubbo.config.ConsumerConfig.isInit()
public java.lang.Boolean com.alibaba.dubbo.config.ConsumerConfig.isGeneric()
public java.lang.String com.alibaba.dubbo.config.ConsumerConfig.getFilter()
public java.lang.String com.alibaba.dubbo.config.ConsumerConfig.getListener()
public java.lang.String com.alibaba.dubbo.config.ConsumerConfig.getVersion()
public com.alibaba.dubbo.config.RegistryConfig com.alibaba.dubbo.config.ConsumerConfig.getRegistry()
public void com.alibaba.dubbo.config.ConsumerConfig.setRegistry(com.alibaba.dubbo.config.RegistryConfig)
public java.util.List com.alibaba.dubbo.config.ConsumerConfig.getRegistries()
public void com.alibaba.dubbo.config.ConsumerConfig.setRegistries(java.util.List)
public com.alibaba.dubbo.config.MonitorConfig com.alibaba.dubbo.config.ConsumerConfig.getMonitor()
public void com.alibaba.dubbo.config.ConsumerConfig.setMonitor(com.alibaba.dubbo.config.MonitorConfig)
public void com.alibaba.dubbo.config.ConsumerConfig.setMonitor(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ConsumerConfig.getCluster()
public void com.alibaba.dubbo.config.ConsumerConfig.setCluster(java.lang.String)
public java.lang.Integer com.alibaba.dubbo.config.ConsumerConfig.getConnections()
public void com.alibaba.dubbo.config.ConsumerConfig.setConnections(java.lang.Integer)
public com.alibaba.dubbo.config.ApplicationConfig com.alibaba.dubbo.config.ConsumerConfig.getApplication()
public void com.alibaba.dubbo.config.ConsumerConfig.setApplication(com.alibaba.dubbo.config.ApplicationConfig)
public com.alibaba.dubbo.config.ModuleConfig com.alibaba.dubbo.config.ConsumerConfig.getModule()
public void com.alibaba.dubbo.config.ConsumerConfig.setModule(com.alibaba.dubbo.config.ModuleConfig)
public java.lang.Integer com.alibaba.dubbo.config.ConsumerConfig.getCallbacks()
public void com.alibaba.dubbo.config.ConsumerConfig.setCallbacks(java.lang.Integer)
public java.lang.String com.alibaba.dubbo.config.ConsumerConfig.getOnconnect()
public java.lang.String com.alibaba.dubbo.config.ConsumerConfig.getOndisconnect()
public java.lang.String com.alibaba.dubbo.config.ConsumerConfig.getLocal()
public void com.alibaba.dubbo.config.ConsumerConfig.setLocal(java.lang.String)
public void com.alibaba.dubbo.config.ConsumerConfig.setLocal(java.lang.Boolean)
public void com.alibaba.dubbo.config.ConsumerConfig.setStub(java.lang.Boolean)
public void com.alibaba.dubbo.config.ConsumerConfig.setStub(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ConsumerConfig.getProxy()
public void com.alibaba.dubbo.config.ConsumerConfig.setProxy(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ConsumerConfig.getLayer()
public void com.alibaba.dubbo.config.ConsumerConfig.setLayer(java.lang.String)
public void com.alibaba.dubbo.config.ConsumerConfig.setScope(java.lang.String)
public void com.alibaba.dubbo.config.ConsumerConfig.setFilter(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ConsumerConfig.getOwner()
public java.lang.String com.alibaba.dubbo.config.ConsumerConfig.getScope()
public void com.alibaba.dubbo.config.ConsumerConfig.setOwner(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ConsumerConfig.getStub()
public java.lang.Integer com.alibaba.dubbo.config.ConsumerConfig.getRetries()
public void com.alibaba.dubbo.config.ConsumerConfig.setRetries(java.lang.Integer)
public java.lang.String com.alibaba.dubbo.config.ConsumerConfig.getLoadbalance()
public void com.alibaba.dubbo.config.ConsumerConfig.setLoadbalance(java.lang.String)
public java.lang.Integer com.alibaba.dubbo.config.ConsumerConfig.getActives()
public void com.alibaba.dubbo.config.ConsumerConfig.setActives(java.lang.Integer)
public java.lang.String com.alibaba.dubbo.config.ConsumerConfig.getMerger()
public void com.alibaba.dubbo.config.ConsumerConfig.setMerger(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ConsumerConfig.getValidation()
public void com.alibaba.dubbo.config.ConsumerConfig.setValidation(java.lang.String)
public java.lang.Boolean com.alibaba.dubbo.config.ConsumerConfig.isAsync()
public void com.alibaba.dubbo.config.ConsumerConfig.setForks(java.lang.Integer)
public java.lang.Integer com.alibaba.dubbo.config.ConsumerConfig.getForks()
public void com.alibaba.dubbo.config.ConsumerConfig.setAsync(java.lang.Boolean)
public java.lang.Boolean com.alibaba.dubbo.config.ConsumerConfig.getSent()
public void com.alibaba.dubbo.config.ConsumerConfig.setSent(java.lang.Boolean)
public java.lang.String com.alibaba.dubbo.config.ConsumerConfig.getMock()
public void com.alibaba.dubbo.config.ConsumerConfig.setMock(java.lang.String)
public void com.alibaba.dubbo.config.ConsumerConfig.setMock(java.lang.Boolean)
public void com.alibaba.dubbo.config.ConsumerConfig.setCache(java.lang.String)
public java.util.Map com.alibaba.dubbo.config.ConsumerConfig.getParameters()
public java.lang.String com.alibaba.dubbo.config.ConsumerConfig.getCache()
public void com.alibaba.dubbo.config.ConsumerConfig.setParameters(java.util.Map)
public java.lang.Integer com.alibaba.dubbo.config.ConsumerConfig.getTimeout()
public void com.alibaba.dubbo.config.ConsumerConfig.setId(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ConsumerConfig.toString()
public java.lang.String com.alibaba.dubbo.config.ConsumerConfig.getId()
public final void com.alibaba.dubbo.config.ConsumerConfig.wait(long,int) throws java.lang.InterruptedException
public final native void com.alibaba.dubbo.config.ConsumerConfig.wait(long) throws java.lang.InterruptedException
public final void com.alibaba.dubbo.config.ConsumerConfig.wait() throws java.lang.InterruptedException
public boolean com.alibaba.dubbo.config.ConsumerConfig.equals(java.lang.Object)
public native int com.alibaba.dubbo.config.ConsumerConfig.hashCode()
public final native java.lang.Class com.alibaba.dubbo.config.ConsumerConfig.getClass()
public final native void com.alibaba.dubbo.config.ConsumerConfig.notify()
public final native void com.alibaba.dubbo.config.ConsumerConfig.notifyAll()
