//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getClient()
public void com.alibaba.dubbo.config.ProviderConfig.setClient(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getThreadpool()
public void com.alibaba.dubbo.config.ProviderConfig.setThreadpool(java.lang.String)
public void com.alibaba.dubbo.config.ProviderConfig.setThreads(java.lang.Integer)
public java.lang.Integer com.alibaba.dubbo.config.ProviderConfig.getQueues()
public void com.alibaba.dubbo.config.ProviderConfig.setQueues(java.lang.Integer)
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getCluster()
public java.lang.Integer com.alibaba.dubbo.config.ProviderConfig.getConnections()
public void com.alibaba.dubbo.config.ProviderConfig.setProtocol(java.lang.String)
public java.lang.Integer com.alibaba.dubbo.config.ProviderConfig.getRetries()
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getLoadbalance()
public java.lang.Integer com.alibaba.dubbo.config.ProviderConfig.getActives()
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getContextpath()
public void com.alibaba.dubbo.config.ProviderConfig.setContextpath(java.lang.String)
public java.lang.Integer com.alibaba.dubbo.config.ProviderConfig.getIothreads()
public void com.alibaba.dubbo.config.ProviderConfig.setIothreads(java.lang.Integer)
public java.lang.Integer com.alibaba.dubbo.config.ProviderConfig.getAccepts()
public void com.alibaba.dubbo.config.ProviderConfig.setAccepts(java.lang.Integer)
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getCharset()
public void com.alibaba.dubbo.config.ProviderConfig.setCharset(java.lang.String)
public java.lang.Integer com.alibaba.dubbo.config.ProviderConfig.getPayload()
public void com.alibaba.dubbo.config.ProviderConfig.setPayload(java.lang.Integer)
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getServer()
public void com.alibaba.dubbo.config.ProviderConfig.setServer(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getTelnet()
public void com.alibaba.dubbo.config.ProviderConfig.setTelnet(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getPrompt()
public void com.alibaba.dubbo.config.ProviderConfig.setPrompt(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getStatus()
public void com.alibaba.dubbo.config.ProviderConfig.setStatus(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getTransporter()
public void com.alibaba.dubbo.config.ProviderConfig.setTransporter(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getExchanger()
public void com.alibaba.dubbo.config.ProviderConfig.setExchanger(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getDispather()
public void com.alibaba.dubbo.config.ProviderConfig.setDispather(java.lang.String)
public void com.alibaba.dubbo.config.ProviderConfig.setDispatcher(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getNetworker()
public void com.alibaba.dubbo.config.ProviderConfig.setNetworker(java.lang.String)
public java.lang.Boolean com.alibaba.dubbo.config.ProviderConfig.isAsync()
public void com.alibaba.dubbo.config.ProviderConfig.setHost(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getCodec()
public void com.alibaba.dubbo.config.ProviderConfig.setCodec(java.lang.String)
public java.lang.Integer com.alibaba.dubbo.config.ProviderConfig.getWait()
public void com.alibaba.dubbo.config.ProviderConfig.setWait(java.lang.Integer)
public java.lang.Boolean com.alibaba.dubbo.config.ProviderConfig.isDefault()
public java.lang.Integer com.alibaba.dubbo.config.ProviderConfig.getThreads()
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getPath()
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getHost()
public java.lang.Integer com.alibaba.dubbo.config.ProviderConfig.getPort()
public void com.alibaba.dubbo.config.ProviderConfig.setDefault(java.lang.Boolean)
public void com.alibaba.dubbo.config.ProviderConfig.setPort(java.lang.Integer)
public void com.alibaba.dubbo.config.ProviderConfig.setPath(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getDispatcher()
public void com.alibaba.dubbo.config.ProviderConfig.setBuffer(java.lang.Integer)
public java.lang.Integer com.alibaba.dubbo.config.ProviderConfig.getBuffer()
public java.lang.Integer com.alibaba.dubbo.config.ProviderConfig.getTimeout()
public void com.alibaba.dubbo.config.ProviderConfig.setVersion(java.lang.String)
public void com.alibaba.dubbo.config.ProviderConfig.setListener(java.lang.String)
public void com.alibaba.dubbo.config.ProviderConfig.setProtocol(com.alibaba.dubbo.config.ProtocolConfig)
public java.lang.Integer com.alibaba.dubbo.config.ProviderConfig.getExecutes()
public void com.alibaba.dubbo.config.ProviderConfig.setExecutes(java.lang.Integer)
public void com.alibaba.dubbo.config.ProviderConfig.setDeprecated(java.lang.Boolean)
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getSerialization()
public void com.alibaba.dubbo.config.ProviderConfig.setSerialization(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getAccesslog()
public void com.alibaba.dubbo.config.ProviderConfig.setAccesslog(java.lang.String)
public void com.alibaba.dubbo.config.ProviderConfig.setAccesslog(java.lang.Boolean)
public java.lang.Boolean com.alibaba.dubbo.config.ProviderConfig.isRegister()
public void com.alibaba.dubbo.config.ProviderConfig.setRegister(java.lang.Boolean)
public java.lang.Boolean com.alibaba.dubbo.config.ProviderConfig.getExport()
public void com.alibaba.dubbo.config.ProviderConfig.setExport(java.lang.Boolean)
public java.lang.Integer com.alibaba.dubbo.config.ProviderConfig.getWeight()
public void com.alibaba.dubbo.config.ProviderConfig.setWeight(java.lang.Integer)
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getDocument()
public void com.alibaba.dubbo.config.ProviderConfig.setDocument(java.lang.String)
public java.lang.Boolean com.alibaba.dubbo.config.ProviderConfig.isDeprecated()
public void com.alibaba.dubbo.config.ProviderConfig.setDynamic(java.lang.Boolean)
public java.util.List com.alibaba.dubbo.config.ProviderConfig.getProtocols()
public void com.alibaba.dubbo.config.ProviderConfig.setProtocols(java.util.List)
public java.lang.Integer com.alibaba.dubbo.config.ProviderConfig.getWarmup()
public void com.alibaba.dubbo.config.ProviderConfig.setWarmup(java.lang.Integer)
public void com.alibaba.dubbo.config.ProviderConfig.setGroup(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getGroup()
public void com.alibaba.dubbo.config.ProviderConfig.setDelay(java.lang.Integer)
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getToken()
public void com.alibaba.dubbo.config.ProviderConfig.setToken(java.lang.String)
public void com.alibaba.dubbo.config.ProviderConfig.setToken(java.lang.Boolean)
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getTag()
public void com.alibaba.dubbo.config.ProviderConfig.setTag(java.lang.String)
public com.alibaba.dubbo.config.ProtocolConfig com.alibaba.dubbo.config.ProviderConfig.getProtocol()
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getFilter()
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getListener()
public java.lang.Boolean com.alibaba.dubbo.config.ProviderConfig.isDynamic()
public java.lang.Integer com.alibaba.dubbo.config.ProviderConfig.getDelay()
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getVersion()
public com.alibaba.dubbo.config.RegistryConfig com.alibaba.dubbo.config.ProviderConfig.getRegistry()
public void com.alibaba.dubbo.config.ProviderConfig.setRegistry(com.alibaba.dubbo.config.RegistryConfig)
public java.util.List com.alibaba.dubbo.config.ProviderConfig.getRegistries()
public void com.alibaba.dubbo.config.ProviderConfig.setRegistries(java.util.List)
public com.alibaba.dubbo.config.MonitorConfig com.alibaba.dubbo.config.ProviderConfig.getMonitor()
public void com.alibaba.dubbo.config.ProviderConfig.setMonitor(com.alibaba.dubbo.config.MonitorConfig)
public void com.alibaba.dubbo.config.ProviderConfig.setMonitor(java.lang.String)
public void com.alibaba.dubbo.config.ProviderConfig.setOnconnect(java.lang.String)
public void com.alibaba.dubbo.config.ProviderConfig.setOndisconnect(java.lang.String)
public void com.alibaba.dubbo.config.ProviderConfig.setCluster(java.lang.String)
public void com.alibaba.dubbo.config.ProviderConfig.setConnections(java.lang.Integer)
public com.alibaba.dubbo.config.ApplicationConfig com.alibaba.dubbo.config.ProviderConfig.getApplication()
public void com.alibaba.dubbo.config.ProviderConfig.setApplication(com.alibaba.dubbo.config.ApplicationConfig)
public com.alibaba.dubbo.config.ModuleConfig com.alibaba.dubbo.config.ProviderConfig.getModule()
public void com.alibaba.dubbo.config.ProviderConfig.setModule(com.alibaba.dubbo.config.ModuleConfig)
public java.lang.Integer com.alibaba.dubbo.config.ProviderConfig.getCallbacks()
public void com.alibaba.dubbo.config.ProviderConfig.setCallbacks(java.lang.Integer)
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getOnconnect()
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getOndisconnect()
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getLocal()
public void com.alibaba.dubbo.config.ProviderConfig.setLocal(java.lang.String)
public void com.alibaba.dubbo.config.ProviderConfig.setLocal(java.lang.Boolean)
public void com.alibaba.dubbo.config.ProviderConfig.setStub(java.lang.Boolean)
public void com.alibaba.dubbo.config.ProviderConfig.setStub(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getProxy()
public void com.alibaba.dubbo.config.ProviderConfig.setProxy(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getLayer()
public void com.alibaba.dubbo.config.ProviderConfig.setLayer(java.lang.String)
public void com.alibaba.dubbo.config.ProviderConfig.setScope(java.lang.String)
public void com.alibaba.dubbo.config.ProviderConfig.setFilter(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getOwner()
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getScope()
public void com.alibaba.dubbo.config.ProviderConfig.setOwner(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getStub()
public void com.alibaba.dubbo.config.ProviderConfig.setTimeout(java.lang.Integer)
public void com.alibaba.dubbo.config.ProviderConfig.setRetries(java.lang.Integer)
public void com.alibaba.dubbo.config.ProviderConfig.setLoadbalance(java.lang.String)
public void com.alibaba.dubbo.config.ProviderConfig.setActives(java.lang.Integer)
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getMerger()
public void com.alibaba.dubbo.config.ProviderConfig.setMerger(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getValidation()
public void com.alibaba.dubbo.config.ProviderConfig.setValidation(java.lang.String)
public void com.alibaba.dubbo.config.ProviderConfig.setForks(java.lang.Integer)
public java.lang.Integer com.alibaba.dubbo.config.ProviderConfig.getForks()
public void com.alibaba.dubbo.config.ProviderConfig.setAsync(java.lang.Boolean)
public java.lang.Boolean com.alibaba.dubbo.config.ProviderConfig.getSent()
public void com.alibaba.dubbo.config.ProviderConfig.setSent(java.lang.Boolean)
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getMock()
public void com.alibaba.dubbo.config.ProviderConfig.setMock(java.lang.String)
public void com.alibaba.dubbo.config.ProviderConfig.setMock(java.lang.Boolean)
public void com.alibaba.dubbo.config.ProviderConfig.setCache(java.lang.String)
public java.util.Map com.alibaba.dubbo.config.ProviderConfig.getParameters()
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getCache()
public void com.alibaba.dubbo.config.ProviderConfig.setParameters(java.util.Map)
public void com.alibaba.dubbo.config.ProviderConfig.setId(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.toString()
public java.lang.String com.alibaba.dubbo.config.ProviderConfig.getId()
public final void com.alibaba.dubbo.config.ProviderConfig.wait(long,int) throws java.lang.InterruptedException
public final native void com.alibaba.dubbo.config.ProviderConfig.wait(long) throws java.lang.InterruptedException
public final void com.alibaba.dubbo.config.ProviderConfig.wait() throws java.lang.InterruptedException
public boolean com.alibaba.dubbo.config.ProviderConfig.equals(java.lang.Object)
public native int com.alibaba.dubbo.config.ProviderConfig.hashCode()
public final native java.lang.Class com.alibaba.dubbo.config.ProviderConfig.getClass()
public final native void com.alibaba.dubbo.config.ProviderConfig.notify()
public final native void com.alibaba.dubbo.config.ProviderConfig.notifyAll()
