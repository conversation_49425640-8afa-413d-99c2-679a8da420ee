//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

public java.lang.String com.alibaba.dubbo.config.ReferenceConfig.getClient()
public void com.alibaba.dubbo.config.ReferenceConfig.setClient(java.lang.String)
public void com.alibaba.dubbo.config.ReferenceConfig.setProtocol(java.lang.String)
public java.lang.Class com.alibaba.dubbo.config.ReferenceConfig.getInterfaceClass()
public void com.alibaba.dubbo.config.ReferenceConfig.setInterfaceClass(java.lang.Class)
public java.lang.String com.alibaba.dubbo.config.ReferenceConfig.getInterface()
public void com.alibaba.dubbo.config.ReferenceConfig.setInterface(java.lang.Class)
public void com.alibaba.dubbo.config.ReferenceConfig.setInterface(java.lang.String)
public void com.alibaba.dubbo.config.ReferenceConfig.setMethods(java.util.List)
public com.alibaba.dubbo.config.ConsumerConfig com.alibaba.dubbo.config.ReferenceConfig.getConsumer()
public void com.alibaba.dubbo.config.ReferenceConfig.setConsumer(com.alibaba.dubbo.config.ConsumerConfig)
public java.lang.String com.alibaba.dubbo.config.ReferenceConfig.getUniqueServiceName()
public java.lang.String com.alibaba.dubbo.config.ReferenceConfig.getGroup()
public com.alibaba.dubbo.common.URL com.alibaba.dubbo.config.ReferenceConfig.toUrl()
public java.util.List com.alibaba.dubbo.config.ReferenceConfig.toUrls()
public java.lang.String com.alibaba.dubbo.config.ReferenceConfig.getUrl()
public void com.alibaba.dubbo.config.ReferenceConfig.setUrl(java.lang.String)
public synchronized java.lang.Object com.alibaba.dubbo.config.ReferenceConfig.get()
public java.util.List com.alibaba.dubbo.config.ReferenceConfig.getMethods()
public synchronized void com.alibaba.dubbo.config.ReferenceConfig.destroy()
public java.lang.String com.alibaba.dubbo.config.ReferenceConfig.getProtocol()
public java.lang.String com.alibaba.dubbo.config.ReferenceConfig.getVersion()
public void com.alibaba.dubbo.config.ReferenceConfig.setVersion(java.lang.String)
public void com.alibaba.dubbo.config.ReferenceConfig.setGeneric(java.lang.Boolean)
public void com.alibaba.dubbo.config.ReferenceConfig.setGeneric(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ReferenceConfig.getGeneric()
public void com.alibaba.dubbo.config.ReferenceConfig.setListener(java.lang.String)
public void com.alibaba.dubbo.config.ReferenceConfig.setOnconnect(java.lang.String)
public void com.alibaba.dubbo.config.ReferenceConfig.setOndisconnect(java.lang.String)
public java.lang.Boolean com.alibaba.dubbo.config.ReferenceConfig.getStubevent()
public java.lang.String com.alibaba.dubbo.config.ReferenceConfig.getReconnect()
public void com.alibaba.dubbo.config.ReferenceConfig.setReconnect(java.lang.String)
public java.lang.Boolean com.alibaba.dubbo.config.ReferenceConfig.getSticky()
public void com.alibaba.dubbo.config.ReferenceConfig.setSticky(java.lang.Boolean)
public java.lang.Boolean com.alibaba.dubbo.config.ReferenceConfig.getLazy()
public void com.alibaba.dubbo.config.ReferenceConfig.setInit(java.lang.Boolean)
public void com.alibaba.dubbo.config.ReferenceConfig.setInjvm(java.lang.Boolean)
public void com.alibaba.dubbo.config.ReferenceConfig.setLazy(java.lang.Boolean)
public java.lang.Boolean com.alibaba.dubbo.config.ReferenceConfig.isInjvm()
public void com.alibaba.dubbo.config.ReferenceConfig.setCheck(java.lang.Boolean)
public java.lang.Boolean com.alibaba.dubbo.config.ReferenceConfig.isCheck()
public void com.alibaba.dubbo.config.ReferenceConfig.setGroup(java.lang.String)
public java.lang.Boolean com.alibaba.dubbo.config.ReferenceConfig.isInit()
public java.lang.Boolean com.alibaba.dubbo.config.ReferenceConfig.isGeneric()
public java.lang.String com.alibaba.dubbo.config.ReferenceConfig.getFilter()
public java.lang.String com.alibaba.dubbo.config.ReferenceConfig.getListener()
public com.alibaba.dubbo.config.RegistryConfig com.alibaba.dubbo.config.ReferenceConfig.getRegistry()
public void com.alibaba.dubbo.config.ReferenceConfig.setRegistry(com.alibaba.dubbo.config.RegistryConfig)
public java.util.List com.alibaba.dubbo.config.ReferenceConfig.getRegistries()
public void com.alibaba.dubbo.config.ReferenceConfig.setRegistries(java.util.List)
public com.alibaba.dubbo.config.MonitorConfig com.alibaba.dubbo.config.ReferenceConfig.getMonitor()
public void com.alibaba.dubbo.config.ReferenceConfig.setMonitor(com.alibaba.dubbo.config.MonitorConfig)
public void com.alibaba.dubbo.config.ReferenceConfig.setMonitor(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ReferenceConfig.getCluster()
public void com.alibaba.dubbo.config.ReferenceConfig.setCluster(java.lang.String)
public java.lang.Integer com.alibaba.dubbo.config.ReferenceConfig.getConnections()
public void com.alibaba.dubbo.config.ReferenceConfig.setConnections(java.lang.Integer)
public com.alibaba.dubbo.config.ApplicationConfig com.alibaba.dubbo.config.ReferenceConfig.getApplication()
public void com.alibaba.dubbo.config.ReferenceConfig.setApplication(com.alibaba.dubbo.config.ApplicationConfig)
public com.alibaba.dubbo.config.ModuleConfig com.alibaba.dubbo.config.ReferenceConfig.getModule()
public void com.alibaba.dubbo.config.ReferenceConfig.setModule(com.alibaba.dubbo.config.ModuleConfig)
public java.lang.Integer com.alibaba.dubbo.config.ReferenceConfig.getCallbacks()
public void com.alibaba.dubbo.config.ReferenceConfig.setCallbacks(java.lang.Integer)
public java.lang.String com.alibaba.dubbo.config.ReferenceConfig.getOnconnect()
public java.lang.String com.alibaba.dubbo.config.ReferenceConfig.getOndisconnect()
public java.lang.String com.alibaba.dubbo.config.ReferenceConfig.getLocal()
public void com.alibaba.dubbo.config.ReferenceConfig.setLocal(java.lang.String)
public void com.alibaba.dubbo.config.ReferenceConfig.setLocal(java.lang.Boolean)
public void com.alibaba.dubbo.config.ReferenceConfig.setStub(java.lang.Boolean)
public void com.alibaba.dubbo.config.ReferenceConfig.setStub(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ReferenceConfig.getProxy()
public void com.alibaba.dubbo.config.ReferenceConfig.setProxy(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ReferenceConfig.getLayer()
public void com.alibaba.dubbo.config.ReferenceConfig.setLayer(java.lang.String)
public void com.alibaba.dubbo.config.ReferenceConfig.setScope(java.lang.String)
public void com.alibaba.dubbo.config.ReferenceConfig.setFilter(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ReferenceConfig.getOwner()
public java.lang.String com.alibaba.dubbo.config.ReferenceConfig.getScope()
public void com.alibaba.dubbo.config.ReferenceConfig.setOwner(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ReferenceConfig.getStub()
public void com.alibaba.dubbo.config.ReferenceConfig.setTimeout(java.lang.Integer)
public java.lang.Integer com.alibaba.dubbo.config.ReferenceConfig.getRetries()
public void com.alibaba.dubbo.config.ReferenceConfig.setRetries(java.lang.Integer)
public java.lang.String com.alibaba.dubbo.config.ReferenceConfig.getLoadbalance()
public void com.alibaba.dubbo.config.ReferenceConfig.setLoadbalance(java.lang.String)
public java.lang.Integer com.alibaba.dubbo.config.ReferenceConfig.getActives()
public void com.alibaba.dubbo.config.ReferenceConfig.setActives(java.lang.Integer)
public java.lang.String com.alibaba.dubbo.config.ReferenceConfig.getMerger()
public void com.alibaba.dubbo.config.ReferenceConfig.setMerger(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ReferenceConfig.getValidation()
public void com.alibaba.dubbo.config.ReferenceConfig.setValidation(java.lang.String)
public java.lang.Boolean com.alibaba.dubbo.config.ReferenceConfig.isAsync()
public void com.alibaba.dubbo.config.ReferenceConfig.setForks(java.lang.Integer)
public java.lang.Integer com.alibaba.dubbo.config.ReferenceConfig.getForks()
public void com.alibaba.dubbo.config.ReferenceConfig.setAsync(java.lang.Boolean)
public java.lang.Boolean com.alibaba.dubbo.config.ReferenceConfig.getSent()
public void com.alibaba.dubbo.config.ReferenceConfig.setSent(java.lang.Boolean)
public java.lang.String com.alibaba.dubbo.config.ReferenceConfig.getMock()
public void com.alibaba.dubbo.config.ReferenceConfig.setMock(java.lang.String)
public void com.alibaba.dubbo.config.ReferenceConfig.setMock(java.lang.Boolean)
public void com.alibaba.dubbo.config.ReferenceConfig.setCache(java.lang.String)
public java.util.Map com.alibaba.dubbo.config.ReferenceConfig.getParameters()
public java.lang.String com.alibaba.dubbo.config.ReferenceConfig.getCache()
public void com.alibaba.dubbo.config.ReferenceConfig.setParameters(java.util.Map)
public java.lang.Integer com.alibaba.dubbo.config.ReferenceConfig.getTimeout()
public void com.alibaba.dubbo.config.ReferenceConfig.setId(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ReferenceConfig.toString()
public java.lang.String com.alibaba.dubbo.config.ReferenceConfig.getId()
public final void com.alibaba.dubbo.config.ReferenceConfig.wait(long,int) throws java.lang.InterruptedException
public final native void com.alibaba.dubbo.config.ReferenceConfig.wait(long) throws java.lang.InterruptedException
public final void com.alibaba.dubbo.config.ReferenceConfig.wait() throws java.lang.InterruptedException
public boolean com.alibaba.dubbo.config.ReferenceConfig.equals(java.lang.Object)
public native int com.alibaba.dubbo.config.ReferenceConfig.hashCode()
public final native java.lang.Class com.alibaba.dubbo.config.ReferenceConfig.getClass()
public final native void com.alibaba.dubbo.config.ReferenceConfig.notify()
public final native void com.alibaba.dubbo.config.ReferenceConfig.notifyAll()
