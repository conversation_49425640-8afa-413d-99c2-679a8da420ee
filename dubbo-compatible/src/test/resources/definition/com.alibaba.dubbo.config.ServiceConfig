//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

public void com.alibaba.dubbo.config.ServiceConfig.setGeneric(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ServiceConfig.getGeneric()
public boolean com.alibaba.dubbo.config.ServiceConfig.isExported()
public boolean com.alibaba.dubbo.config.ServiceConfig.isUnexported()
public void com.alibaba.dubbo.config.ServiceConfig.setProvider(com.alibaba.dubbo.config.ProviderConfig)
public java.util.List com.alibaba.dubbo.config.ServiceConfig.getExportedUrls()
public void com.alibaba.dubbo.config.ServiceConfig.setProviders(java.util.List)
public java.lang.Class com.alibaba.dubbo.config.ServiceConfig.getInterfaceClass()
public void com.alibaba.dubbo.config.ServiceConfig.setInterfaceClass(java.lang.Class)
public java.lang.String com.alibaba.dubbo.config.ServiceConfig.getInterface()
public void com.alibaba.dubbo.config.ServiceConfig.setInterface(java.lang.String)
public void com.alibaba.dubbo.config.ServiceConfig.setInterface(java.lang.Class)
public void com.alibaba.dubbo.config.ServiceConfig.setMethods(java.util.List)
public java.lang.String com.alibaba.dubbo.config.ServiceConfig.getUniqueServiceName()
public java.lang.String com.alibaba.dubbo.config.ServiceConfig.getGroup()
public void com.alibaba.dubbo.config.ServiceConfig.setMock(java.lang.Boolean)
public void com.alibaba.dubbo.config.ServiceConfig.setMock(java.lang.String)
public com.alibaba.dubbo.common.URL com.alibaba.dubbo.config.ServiceConfig.toUrl()
public java.util.List com.alibaba.dubbo.config.ServiceConfig.toUrls()
public synchronized void com.alibaba.dubbo.config.ServiceConfig.unexport()
public java.util.List com.alibaba.dubbo.config.ServiceConfig.getMethods()
public java.lang.String com.alibaba.dubbo.config.ServiceConfig.getPath()
public java.lang.Object com.alibaba.dubbo.config.ServiceConfig.getRef()
public synchronized void com.alibaba.dubbo.config.ServiceConfig.export()
public java.util.List com.alibaba.dubbo.config.ServiceConfig.getProviders()
public com.alibaba.dubbo.config.ProviderConfig com.alibaba.dubbo.config.ServiceConfig.getProvider()
public java.lang.String com.alibaba.dubbo.config.ServiceConfig.getVersion()
public void com.alibaba.dubbo.config.ServiceConfig.setPath(java.lang.String)
public void com.alibaba.dubbo.config.ServiceConfig.setRef(java.lang.Object)
public void com.alibaba.dubbo.config.ServiceConfig.setVersion(java.lang.String)
public void com.alibaba.dubbo.config.ServiceConfig.setListener(java.lang.String)
public void com.alibaba.dubbo.config.ServiceConfig.setProtocol(com.alibaba.dubbo.config.ProtocolConfig)
public java.lang.Integer com.alibaba.dubbo.config.ServiceConfig.getExecutes()
public void com.alibaba.dubbo.config.ServiceConfig.setExecutes(java.lang.Integer)
public void com.alibaba.dubbo.config.ServiceConfig.setDeprecated(java.lang.Boolean)
public java.lang.String com.alibaba.dubbo.config.ServiceConfig.getSerialization()
public void com.alibaba.dubbo.config.ServiceConfig.setSerialization(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ServiceConfig.getAccesslog()
public void com.alibaba.dubbo.config.ServiceConfig.setAccesslog(java.lang.String)
public void com.alibaba.dubbo.config.ServiceConfig.setAccesslog(java.lang.Boolean)
public java.lang.Boolean com.alibaba.dubbo.config.ServiceConfig.isRegister()
public void com.alibaba.dubbo.config.ServiceConfig.setRegister(java.lang.Boolean)
public java.lang.Boolean com.alibaba.dubbo.config.ServiceConfig.getExport()
public void com.alibaba.dubbo.config.ServiceConfig.setExport(java.lang.Boolean)
public java.lang.Integer com.alibaba.dubbo.config.ServiceConfig.getWeight()
public void com.alibaba.dubbo.config.ServiceConfig.setWeight(java.lang.Integer)
public java.lang.String com.alibaba.dubbo.config.ServiceConfig.getDocument()
public void com.alibaba.dubbo.config.ServiceConfig.setDocument(java.lang.String)
public java.lang.Boolean com.alibaba.dubbo.config.ServiceConfig.isDeprecated()
public void com.alibaba.dubbo.config.ServiceConfig.setDynamic(java.lang.Boolean)
public java.util.List com.alibaba.dubbo.config.ServiceConfig.getProtocols()
public void com.alibaba.dubbo.config.ServiceConfig.setProtocols(java.util.List)
public java.lang.Integer com.alibaba.dubbo.config.ServiceConfig.getWarmup()
public void com.alibaba.dubbo.config.ServiceConfig.setWarmup(java.lang.Integer)
public void com.alibaba.dubbo.config.ServiceConfig.setGroup(java.lang.String)
public void com.alibaba.dubbo.config.ServiceConfig.setDelay(java.lang.Integer)
public java.lang.String com.alibaba.dubbo.config.ServiceConfig.getToken()
public void com.alibaba.dubbo.config.ServiceConfig.setToken(java.lang.String)
public void com.alibaba.dubbo.config.ServiceConfig.setToken(java.lang.Boolean)
public java.lang.String com.alibaba.dubbo.config.ServiceConfig.getTag()
public void com.alibaba.dubbo.config.ServiceConfig.setTag(java.lang.String)
public com.alibaba.dubbo.config.ProtocolConfig com.alibaba.dubbo.config.ServiceConfig.getProtocol()
public java.lang.String com.alibaba.dubbo.config.ServiceConfig.getFilter()
public java.lang.String com.alibaba.dubbo.config.ServiceConfig.getListener()
public java.lang.Boolean com.alibaba.dubbo.config.ServiceConfig.isDynamic()
public java.lang.Integer com.alibaba.dubbo.config.ServiceConfig.getDelay()
public com.alibaba.dubbo.config.RegistryConfig com.alibaba.dubbo.config.ServiceConfig.getRegistry()
public void com.alibaba.dubbo.config.ServiceConfig.setRegistry(com.alibaba.dubbo.config.RegistryConfig)
public java.util.List com.alibaba.dubbo.config.ServiceConfig.getRegistries()
public void com.alibaba.dubbo.config.ServiceConfig.setRegistries(java.util.List)
public com.alibaba.dubbo.config.MonitorConfig com.alibaba.dubbo.config.ServiceConfig.getMonitor()
public void com.alibaba.dubbo.config.ServiceConfig.setMonitor(com.alibaba.dubbo.config.MonitorConfig)
public void com.alibaba.dubbo.config.ServiceConfig.setMonitor(java.lang.String)
public void com.alibaba.dubbo.config.ServiceConfig.setOnconnect(java.lang.String)
public void com.alibaba.dubbo.config.ServiceConfig.setOndisconnect(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ServiceConfig.getCluster()
public void com.alibaba.dubbo.config.ServiceConfig.setCluster(java.lang.String)
public java.lang.Integer com.alibaba.dubbo.config.ServiceConfig.getConnections()
public void com.alibaba.dubbo.config.ServiceConfig.setConnections(java.lang.Integer)
public com.alibaba.dubbo.config.ApplicationConfig com.alibaba.dubbo.config.ServiceConfig.getApplication()
public void com.alibaba.dubbo.config.ServiceConfig.setApplication(com.alibaba.dubbo.config.ApplicationConfig)
public com.alibaba.dubbo.config.ModuleConfig com.alibaba.dubbo.config.ServiceConfig.getModule()
public void com.alibaba.dubbo.config.ServiceConfig.setModule(com.alibaba.dubbo.config.ModuleConfig)
public java.lang.Integer com.alibaba.dubbo.config.ServiceConfig.getCallbacks()
public void com.alibaba.dubbo.config.ServiceConfig.setCallbacks(java.lang.Integer)
public java.lang.String com.alibaba.dubbo.config.ServiceConfig.getOnconnect()
public java.lang.String com.alibaba.dubbo.config.ServiceConfig.getOndisconnect()
public java.lang.String com.alibaba.dubbo.config.ServiceConfig.getLocal()
public void com.alibaba.dubbo.config.ServiceConfig.setLocal(java.lang.String)
public void com.alibaba.dubbo.config.ServiceConfig.setLocal(java.lang.Boolean)
public void com.alibaba.dubbo.config.ServiceConfig.setStub(java.lang.Boolean)
public void com.alibaba.dubbo.config.ServiceConfig.setStub(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ServiceConfig.getProxy()
public void com.alibaba.dubbo.config.ServiceConfig.setProxy(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ServiceConfig.getLayer()
public void com.alibaba.dubbo.config.ServiceConfig.setLayer(java.lang.String)
public void com.alibaba.dubbo.config.ServiceConfig.setScope(java.lang.String)
public void com.alibaba.dubbo.config.ServiceConfig.setFilter(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ServiceConfig.getOwner()
public java.lang.String com.alibaba.dubbo.config.ServiceConfig.getScope()
public void com.alibaba.dubbo.config.ServiceConfig.setOwner(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ServiceConfig.getStub()
public void com.alibaba.dubbo.config.ServiceConfig.setTimeout(java.lang.Integer)
public java.lang.Integer com.alibaba.dubbo.config.ServiceConfig.getRetries()
public void com.alibaba.dubbo.config.ServiceConfig.setRetries(java.lang.Integer)
public java.lang.String com.alibaba.dubbo.config.ServiceConfig.getLoadbalance()
public void com.alibaba.dubbo.config.ServiceConfig.setLoadbalance(java.lang.String)
public java.lang.Integer com.alibaba.dubbo.config.ServiceConfig.getActives()
public void com.alibaba.dubbo.config.ServiceConfig.setActives(java.lang.Integer)
public java.lang.String com.alibaba.dubbo.config.ServiceConfig.getMerger()
public void com.alibaba.dubbo.config.ServiceConfig.setMerger(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ServiceConfig.getValidation()
public void com.alibaba.dubbo.config.ServiceConfig.setValidation(java.lang.String)
public java.lang.Boolean com.alibaba.dubbo.config.ServiceConfig.isAsync()
public void com.alibaba.dubbo.config.ServiceConfig.setForks(java.lang.Integer)
public java.lang.Integer com.alibaba.dubbo.config.ServiceConfig.getForks()
public void com.alibaba.dubbo.config.ServiceConfig.setAsync(java.lang.Boolean)
public java.lang.Boolean com.alibaba.dubbo.config.ServiceConfig.getSent()
public void com.alibaba.dubbo.config.ServiceConfig.setSent(java.lang.Boolean)
public java.lang.String com.alibaba.dubbo.config.ServiceConfig.getMock()
public void com.alibaba.dubbo.config.ServiceConfig.setCache(java.lang.String)
public java.util.Map com.alibaba.dubbo.config.ServiceConfig.getParameters()
public java.lang.String com.alibaba.dubbo.config.ServiceConfig.getCache()
public void com.alibaba.dubbo.config.ServiceConfig.setParameters(java.util.Map)
public java.lang.Integer com.alibaba.dubbo.config.ServiceConfig.getTimeout()
public void com.alibaba.dubbo.config.ServiceConfig.setId(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.ServiceConfig.toString()
public java.lang.String com.alibaba.dubbo.config.ServiceConfig.getId()
public final void com.alibaba.dubbo.config.ServiceConfig.wait(long,int) throws java.lang.InterruptedException
public final native void com.alibaba.dubbo.config.ServiceConfig.wait(long) throws java.lang.InterruptedException
public final void com.alibaba.dubbo.config.ServiceConfig.wait() throws java.lang.InterruptedException
public boolean com.alibaba.dubbo.config.ServiceConfig.equals(java.lang.Object)
public native int com.alibaba.dubbo.config.ServiceConfig.hashCode()
public final native java.lang.Class com.alibaba.dubbo.config.ServiceConfig.getClass()
public final native void com.alibaba.dubbo.config.ServiceConfig.notify()
public final native void com.alibaba.dubbo.config.ServiceConfig.notifyAll()
