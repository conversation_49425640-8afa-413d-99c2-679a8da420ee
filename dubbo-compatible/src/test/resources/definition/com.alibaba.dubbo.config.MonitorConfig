//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

public void com.alibaba.dubbo.config.MonitorConfig.setVersion(java.lang.String)
public void com.alibaba.dubbo.config.MonitorConfig.setProtocol(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.MonitorConfig.getUsername()
public void com.alibaba.dubbo.config.MonitorConfig.setUsername(java.lang.String)
public void com.alibaba.dubbo.config.MonitorConfig.setPassword(java.lang.String)
public void com.alibaba.dubbo.config.MonitorConfig.setInterval(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.MonitorConfig.getInterval()
public void com.alibaba.dubbo.config.MonitorConfig.setGroup(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.MonitorConfig.getGroup()
public java.lang.String com.alibaba.dubbo.config.MonitorConfig.getAddress()
public java.lang.Boolean com.alibaba.dubbo.config.MonitorConfig.isDefault()
public java.util.Map com.alibaba.dubbo.config.MonitorConfig.getParameters()
public java.lang.String com.alibaba.dubbo.config.MonitorConfig.getProtocol()
public void com.alibaba.dubbo.config.MonitorConfig.setDefault(java.lang.Boolean)
public java.lang.String com.alibaba.dubbo.config.MonitorConfig.getPassword()
public void com.alibaba.dubbo.config.MonitorConfig.setAddress(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.MonitorConfig.getVersion()
public void com.alibaba.dubbo.config.MonitorConfig.setParameters(java.util.Map)
public void com.alibaba.dubbo.config.MonitorConfig.setId(java.lang.String)
public java.lang.String com.alibaba.dubbo.config.MonitorConfig.toString()
public java.lang.String com.alibaba.dubbo.config.MonitorConfig.getId()
public final void com.alibaba.dubbo.config.MonitorConfig.wait(long,int) throws java.lang.InterruptedException
public final native void com.alibaba.dubbo.config.MonitorConfig.wait(long) throws java.lang.InterruptedException
public final void com.alibaba.dubbo.config.MonitorConfig.wait() throws java.lang.InterruptedException
public boolean com.alibaba.dubbo.config.MonitorConfig.equals(java.lang.Object)
public native int com.alibaba.dubbo.config.MonitorConfig.hashCode()
public final native java.lang.Class com.alibaba.dubbo.config.MonitorConfig.getClass()
public final native void com.alibaba.dubbo.config.MonitorConfig.notify()
public final native void com.alibaba.dubbo.config.MonitorConfig.notifyAll()
