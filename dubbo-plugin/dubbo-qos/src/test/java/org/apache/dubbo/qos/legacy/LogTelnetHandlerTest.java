/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.dubbo.qos.legacy;

import org.apache.dubbo.remoting.Channel;
import org.apache.dubbo.remoting.RemotingException;
import org.apache.dubbo.remoting.telnet.TelnetHandler;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;

/**
 * LogTelnetHandlerTest.java
 */
class LogTelnetHandlerTest {

    private static TelnetHandler log = new LogTelnetHandler();
    private Channel mockChannel;

    @Test
    void testChangeLogLevel() throws RemotingException {
        mockChannel = mock(Channel.class);

        String result = log.telnet(mockChannel, "error");
        assertTrue(result.contains("\r\nCURRENT LOG LEVEL:ERROR"));
        String result2 = log.telnet(mockChannel, "warn");
        assertTrue(result2.contains("\r\nCURRENT LOG LEVEL:WARN"));
    }

    @Test
    void testPrintLog() throws RemotingException {
        mockChannel = mock(Channel.class);

        String result = log.telnet(mockChannel, "100");
        assertTrue(result.contains("CURRENT LOG APPENDER"));
    }
}
