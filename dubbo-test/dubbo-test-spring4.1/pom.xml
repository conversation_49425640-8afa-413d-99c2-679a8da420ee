<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.apache.dubbo</groupId>
    <artifactId>dubbo-test</artifactId>
    <version>${revision}</version>
    <relativePath>../pom.xml</relativePath>
  </parent>

  <artifactId>dubbo-test-spring4.1</artifactId>

  <properties>
    <skip_maven_deploy>true</skip_maven_deploy>
    <spring_version>5.3.39</spring_version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-framework-bom</artifactId>
        <version>${spring_version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <dependencies>
    <!-- spring test -->
    <dependency>
      <groupId>org.apache.dubbo</groupId>
      <artifactId>dubbo-test-spring</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.curator</groupId>
      <artifactId>curator-framework</artifactId>
      <!--            <scope>test</scope>-->
    </dependency>
    <dependency>
      <groupId>org.apache.curator</groupId>
      <artifactId>curator-recipes</artifactId>
      <!--            <scope>test</scope>-->
    </dependency>
    <dependency>
      <groupId>org.apache.zookeeper</groupId>
      <artifactId>zookeeper</artifactId>
      <!--            <scope>test</scope>-->
    </dependency>

  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <dependenciesToScan>
            <dependency>org.apache.dubbo:dubbo-test-spring</dependency>
          </dependenciesToScan>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>jdk15ge-add-open</id>
      <activation>
        <jdk>[15,</jdk>
      </activation>
      <properties>
        <argLine>--add-opens java.base/java.lang=ALL-UNNAMED
          --add-opens java.base/java.math=ALL-UNNAMED
          --add-opens java.base/java.util=ALL-UNNAMED</argLine>
      </properties>
    </profile>
    <profile>
      <id>jdk15ge-simple</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <useSystemClassLoader>true</useSystemClassLoader>
              <forkCount>1</forkCount>
              <systemProperties>
                <!-- common shared -->
              </systemProperties>
              <!-- Activate the use of TCP to transmit events to the plugin to fix Corrupted STDOUT issue -->
              <forkNode implementation="org.apache.maven.plugin.surefire.extensions.SurefireForkNodeFactory" />
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
