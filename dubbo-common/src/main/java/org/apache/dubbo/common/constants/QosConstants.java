/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.dubbo.common.constants;

/**
 * QosConstants
 */
public interface QosConstants {

    String QOS_ENABLE = "qos.enable";

    String QOS_CHECK = "qos.check";

    String QOS_HOST = "qos.host";

    String QOS_PORT = "qos.port";

    String ACCEPT_FOREIGN_IP = "qos.accept.foreign.ip";

    String ACCEPT_FOREIGN_IP_WHITELIST = "qos.accept.foreign.ip.whitelist";

    String ANONYMOUS_ACCESS_PERMISSION_LEVEL = "qos.anonymous.access.permission.level";

    String ANONYMOUS_ACCESS_ALLOW_COMMANDS = "qos.anonymous.access.allow.commands";

    String QOS_ENABLE_COMPATIBLE = "qos-enable";

    String QOS_HOST_COMPATIBLE = "qos-host";

    String QOS_PORT_COMPATIBLE = "qos-port";

    String ACCEPT_FOREIGN_IP_COMPATIBLE = "qos-accept-foreign-ip";

    String ACCEPT_FOREIGN_IP_WHITELIST_COMPATIBLE = "qos-accept-foreign-ip-whitelist";

    String ANONYMOUS_ACCESS_PERMISSION_LEVEL_COMPATIBLE = "qos-anonymous-access-permission-level";
}
