# EnvoyFilter to add hash policy to all routes
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: dubbo-hash-policy
  namespace: dubbo-proxyless
spec:
  workloadSelector:
    labels:
      app: dubbo-demo-xds-consumer
  configPatches:
  - applyTo: HTTP_ROUTE
    match:
      context: SIDECAR_OUTBOUND
    patch:
      operation: MERGE
      value:
        route:
          hash_policy:
          - header:
              header_name: "user-id"
              terminal: false
