# EnvoyFilter to add hash policy to the route
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: dubbo-hash-policy
  namespace: dubbo-proxyless
spec:
  workloadSelector:
    labels:
      app: dubbo-demo-xds-consumer
  configPatches:
  - applyTo: HTTP_ROUTE
    match:
      context: SIDECAR_OUTBOUND
      routeConfiguration:
        vhost:
          name: "outbound|50051||dubbo-demo-xds-provider.dubbo-proxyless.svc.cluster.local"
          route:
            match:
              headers:
              - name: "test-scenario"
                exact_match: "hash-test"
    patch:
      operation: MERGE
      value:
        route:
          hash_policy:
          - header:
              header_name: "user-id"
