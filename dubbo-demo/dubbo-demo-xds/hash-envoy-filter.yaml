# EnvoyFilter to add hash policy to the route
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: dubbo-hash-policy
  namespace: dubbo-proxyless
spec:
  configPatches:
  - applyTo: HTTP_ROUTE
    match:
      context: SIDECAR_INBOUND
      listener:
        filterChain:
          filter:
            name: "envoy.filters.network.http_connection_manager"
            subFilter:
              name: "envoy.filters.http.router"
      routeConfiguration:
        vhost:
          name: "inbound|http|50051"
          route:
            name: "default"
    patch:
      operation: MERGE
      value:
        route:
          hash_policy:
          - header:
              header_name: "user-id"
