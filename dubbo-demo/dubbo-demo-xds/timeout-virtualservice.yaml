# 测试超时配置的VirtualService
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: dubbo-timeout-test
  namespace: dubbo-proxyless
spec:
  hosts:
  - dubbo-demo-xds-provider.dubbo-proxyless.svc.cluster.local
  http:
  - match:
    - headers:
        test-scenario:
          exact: "timeout-2s"
    timeout: 2s  # 测试2秒超时
    route:
    - destination:
        host: dubbo-demo-xds-provider.dubbo-proxyless.svc.cluster.local

  - match:
    - headers:
        test-scenario:
          exact: "timeout-5s"
    timeout: 5s  # 测试5秒超时
    route:
    - destination:
        host: dubbo-demo-xds-provider.dubbo-proxyless.svc.cluster.local