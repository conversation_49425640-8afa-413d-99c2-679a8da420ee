# 组合多种策略的VirtualService
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: dubbo-combined-test
  namespace: dubbo-proxyless
spec:
  hosts:
  - dubbo-demo-xds-provider.dubbo-proxyless.svc.cluster.local
  http:
  # 超时测试路由
  - match:
    - headers:
        test-scenario:
          exact: "timeout-2s"
    route:
    - destination:
        host: dubbo-demo-xds-provider.dubbo-proxyless.svc.cluster.local
    timeout: 2s
    
  - match:
    - headers:
        test-scenario:
          exact: "timeout-5s"
    route:
    - destination:
        host: dubbo-demo-xds-provider.dubbo-proxyless.svc.cluster.local
    timeout: 5s
    
  # 重试测试路由
  - match:
    - headers:
        test-scenario:
          exact: "retry-test"
    route:
    - destination:
        host: dubbo-demo-xds-provider.dubbo-proxyless.svc.cluster.local
    timeout: 10s
    retries:
      attempts: 3
      perTryTimeout: 2s
      retryOn: "5xx,reset,connect-failure,refused-stream,deadline-exceeded,internal,resource-exhausted,unavailable,cancelled"
      
  # 哈希路由测试
  - match:
    - headers:
        test-scenario:
          exact: "hash-test"
    route:
    - destination:
        host: dubbo-demo-xds-provider.dubbo-proxyless.svc.cluster.local
    hashPolicy:
    - header:
        name: "user-id"
        
  # 权重路由测试
  - match:
    - headers:
        test-scenario:
          exact: "weight-test"
    route:
    - destination:
        host: dubbo-demo-xds-provider.dubbo-proxyless.svc.cluster.local
        subset: v1
      weight: 70
    - destination:
        host: dubbo-demo-xds-provider.dubbo-proxyless.svc.cluster.local
        subset: v2
      weight: 30
      
  # 默认路由（正常请求）
  - route:
    - destination:
        host: dubbo-demo-xds-provider.dubbo-proxyless.svc.cluster.local
