#!/bin/bash

# 快速测试XDS策略的脚本
set -e

NAMESPACE="dubbo-proxyless"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 检查Pod状态
check_pods() {
    log_info "Checking pod status..."
    kubectl get pods -n $NAMESPACE -l app=dubbo-demo-xds-consumer
    kubectl get pods -n $NAMESPACE -l app=dubbo-demo-xds-provider
}

# 应用配置并测试
test_config() {
    local config_file=$1
    local test_name=$2
    local duration=${3:-30}
    
    log_info "=== Testing $test_name ==="
    
    # 应用配置
    log_info "Applying $config_file..."
    kubectl apply -f $config_file
    
    # 等待配置生效
    log_info "Waiting for configuration to take effect..."
    sleep 10
    
    # 监控日志
    log_info "Monitoring logs for $duration seconds..."
    timeout $duration kubectl logs -n $NAMESPACE -l app=dubbo-demo-xds-consumer -f --tail=20 &
    timeout $duration kubectl logs -n $NAMESPACE -l app=dubbo-demo-xds-provider -f --tail=20 &
    
    sleep $duration
    
    # 清理配置
    log_info "Cleaning up $config_file..."
    kubectl delete -f $config_file --ignore-not-found=true
    
    log_success "$test_name test completed"
    echo "----------------------------------------"
    sleep 5
}

# 主测试流程
main() {
    log_info "Starting XDS Strategy Quick Tests..."
    
    # 检查环境
    check_pods
    
    # 应用基础DestinationRule
    log_info "Applying DestinationRule..."
    kubectl apply -f destination-rule.yaml
    sleep 10
    
    # 测试各种策略
    test_config "timeout-virtualservice.yaml" "Timeout Strategy" 45
    test_config "retry-virtualservice.yaml" "Retry Strategy" 60
    test_config "weight-virtualservice.yaml" "Weight Routing" 90
    test_config "hash-virtualservice.yaml" "Hash Routing" 60
    
    # 清理DestinationRule
    log_info "Cleaning up DestinationRule..."
    kubectl delete -f destination-rule.yaml --ignore-not-found=true
    
    log_success "All tests completed!"
    
    # 显示如何查看详细日志
    echo ""
    log_info "To view detailed logs, run:"
    echo "kubectl logs -n $NAMESPACE -l app=dubbo-demo-xds-consumer --tail=100"
    echo "kubectl logs -n $NAMESPACE -l app=dubbo-demo-xds-provider --tail=100"
}

# 单独测试函数
test_timeout() {
    kubectl apply -f destination-rule.yaml
    test_config "timeout-virtualservice.yaml" "Timeout Strategy" 45
    kubectl delete -f destination-rule.yaml --ignore-not-found=true
}

test_retry() {
    kubectl apply -f destination-rule.yaml
    test_config "retry-virtualservice.yaml" "Retry Strategy" 60
    kubectl delete -f destination-rule.yaml --ignore-not-found=true
}

test_weight() {
    kubectl apply -f destination-rule.yaml
    test_config "weight-virtualservice.yaml" "Weight Routing" 90
    kubectl delete -f destination-rule.yaml --ignore-not-found=true
}

test_hash() {
    kubectl apply -f destination-rule.yaml
    test_config "hash-virtualservice.yaml" "Hash Routing" 60
    kubectl delete -f destination-rule.yaml --ignore-not-found=true
}

# 脚本参数处理
case "${1:-all}" in
    "timeout")
        test_timeout
        ;;
    "retry")
        test_retry
        ;;
    "weight")
        test_weight
        ;;
    "hash")
        test_hash
        ;;
    "all"|*)
        main
        ;;
esac
