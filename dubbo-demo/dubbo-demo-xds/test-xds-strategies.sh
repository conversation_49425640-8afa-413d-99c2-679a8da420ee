#!/bin/bash

# XDS策略自动化测试脚本
# 用于验证timeout、retry、weight、hash等策略是否生效

set -e

NAMESPACE="dubbo-proxyless"
CONSUMER_POD=""
PROVIDER_V1_POD=""
PROVIDER_V2_POD=""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取Pod名称
get_pod_names() {
    log_info "Getting pod names..."
    
    CONSUMER_POD=$(kubectl get pods -n $NAMESPACE -l app=dubbo-demo-xds-consumer -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")
    PROVIDER_V1_POD=$(kubectl get pods -n $NAMESPACE -l app=dubbo-demo-xds-provider,version=v1 -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")
    PROVIDER_V2_POD=$(kubectl get pods -n $NAMESPACE -l app=dubbo-demo-xds-provider,version=v2 -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")
    
    if [[ -z "$CONSUMER_POD" ]]; then
        log_error "Consumer pod not found"
        exit 1
    fi
    
    log_info "Consumer Pod: $CONSUMER_POD"
    log_info "Provider V1 Pod: $PROVIDER_V1_POD"
    log_info "Provider V2 Pod: $PROVIDER_V2_POD"
}

# 应用VirtualService配置
apply_virtualservice() {
    local config_file=$1
    local test_name=$2
    
    log_info "Applying VirtualService for $test_name test..."
    kubectl apply -f $config_file -n $NAMESPACE
    
    # 等待配置生效
    log_info "Waiting for configuration to take effect..."
    sleep 10
}

# 清理VirtualService配置
cleanup_virtualservice() {
    local config_file=$1
    
    log_info "Cleaning up VirtualService..."
    kubectl delete -f $config_file -n $NAMESPACE --ignore-not-found=true
    sleep 5
}

# 测试超时策略
test_timeout_strategy() {
    log_info "=== Testing Timeout Strategy ==="
    
    apply_virtualservice "timeout-virtualservice.yaml" "timeout"
    
    # 测试2秒超时
    log_info "Testing 2s timeout..."
    kubectl exec -n $NAMESPACE $CONSUMER_POD -- curl -s -H "test-scenario: timeout-2s" \
        http://localhost:8080/test/timeout-2s || log_warning "2s timeout test completed (expected timeout)"
    
    # 测试5秒超时
    log_info "Testing 5s timeout..."
    kubectl exec -n $NAMESPACE $CONSUMER_POD -- curl -s -H "test-scenario: timeout-5s" \
        http://localhost:8080/test/timeout-5s && log_success "5s timeout test passed"
    
    cleanup_virtualservice "timeout-virtualservice.yaml"
}

# 测试重试策略
test_retry_strategy() {
    log_info "=== Testing Retry Strategy ==="
    
    apply_virtualservice "retry-virtualservice.yaml" "retry"
    
    log_info "Testing retry mechanism..."
    for i in {1..5}; do
        log_info "Retry test attempt $i..."
        kubectl exec -n $NAMESPACE $CONSUMER_POD -- curl -s -H "test-scenario: retry-test" \
            http://localhost:8080/test/retry-test || log_info "Retry test $i completed"
        sleep 2
    done
    
    cleanup_virtualservice "retry-virtualservice.yaml"
}

# 测试权重路由
test_weight_routing() {
    log_info "=== Testing Weight Routing Strategy ==="
    
    apply_virtualservice "weight-virtualservice.yaml" "weight"
    
    log_info "Sending 100 requests to test weight distribution..."
    
    # 创建临时文件记录结果
    local result_file="/tmp/weight_test_results.txt"
    > $result_file
    
    for i in {1..100}; do
        result=$(kubectl exec -n $NAMESPACE $CONSUMER_POD -- curl -s -H "test-scenario: weight-test" \
            http://localhost:8080/test/weight-test 2>/dev/null || echo "error")
        echo "$result" >> $result_file
        
        if [[ $((i % 20)) -eq 0 ]]; then
            log_info "Progress: $i/100 requests completed"
        fi
    done
    
    # 分析结果
    local v1_count=$(grep -c "v1" $result_file || echo "0")
    local v2_count=$(grep -c "v2" $result_file || echo "0")
    local total_count=$((v1_count + v2_count))
    
    if [[ $total_count -gt 0 ]]; then
        local v1_percentage=$((v1_count * 100 / total_count))
        local v2_percentage=$((v2_count * 100 / total_count))
        
        log_info "Weight routing results:"
        log_info "  V1: $v1_count requests ($v1_percentage%)"
        log_info "  V2: $v2_count requests ($v2_percentage%)"
        
        # 验证权重分配（期望v1:70%, v2:30%，允许±15%误差）
        if [[ $v1_percentage -ge 55 && $v1_percentage -le 85 && $v2_percentage -ge 15 && $v2_percentage -le 45 ]]; then
            log_success "Weight routing strategy is WORKING correctly"
        else
            log_warning "Weight routing strategy might NOT be working as expected"
        fi
    else
        log_error "No successful responses received for weight test"
    fi
    
    rm -f $result_file
    cleanup_virtualservice "weight-virtualservice.yaml"
}

# 测试一致性哈希路由
test_hash_routing() {
    log_info "=== Testing Hash Routing Strategy ==="
    
    apply_virtualservice "hash-virtualservice.yaml" "hash"
    
    local users=("user1" "user2" "user3" "user4" "user5")
    local result_file="/tmp/hash_test_results.txt"
    > $result_file
    
    # 每个用户发送10次请求
    for user in "${users[@]}"; do
        log_info "Testing consistency for $user..."
        
        for i in {1..10}; do
            result=$(kubectl exec -n $NAMESPACE $CONSUMER_POD -- curl -s \
                -H "test-scenario: hash-test" \
                -H "user-id: $user" \
                http://localhost:8080/test/hash-test 2>/dev/null || echo "error")
            echo "$user:$result" >> $result_file
        done
    done
    
    # 分析一致性
    log_info "Analyzing hash routing consistency..."
    for user in "${users[@]}"; do
        local user_results=$(grep "^$user:" $result_file | cut -d: -f2- | sort | uniq)
        local unique_count=$(echo "$user_results" | wc -l)
        
        if [[ $unique_count -eq 1 ]]; then
            local version=$(echo "$user_results" | grep -o "v[12]" | head -1)
            log_success "$user consistently routed to $version"
        else
            log_warning "$user routing is NOT consistent ($unique_count different responses)"
        fi
    done
    
    # 检查是否有不同的版本被使用
    local versions_used=$(grep -o "v[12]" $result_file | sort | uniq | wc -l)
    if [[ $versions_used -gt 1 ]]; then
        log_success "Hash routing uses multiple versions ($versions_used versions)"
    else
        log_warning "Hash routing might not be distributing across versions"
    fi
    
    rm -f $result_file
    cleanup_virtualservice "hash-virtualservice.yaml"
}

# 监控日志
monitor_logs() {
    local duration=${1:-30}
    log_info "Monitoring logs for $duration seconds..."
    
    # 在后台监控Consumer日志
    kubectl logs -n $NAMESPACE -f $CONSUMER_POD --tail=50 > /tmp/consumer_logs.txt 2>&1 &
    local consumer_log_pid=$!
    
    # 在后台监控Provider日志
    if [[ -n "$PROVIDER_V1_POD" ]]; then
        kubectl logs -n $NAMESPACE -f $PROVIDER_V1_POD --tail=50 > /tmp/provider_v1_logs.txt 2>&1 &
        local provider_v1_log_pid=$!
    fi
    
    if [[ -n "$PROVIDER_V2_POD" ]]; then
        kubectl logs -n $NAMESPACE -f $PROVIDER_V2_POD --tail=50 > /tmp/provider_v2_logs.txt 2>&1 &
        local provider_v2_log_pid=$!
    fi
    
    sleep $duration
    
    # 停止日志监控
    kill $consumer_log_pid 2>/dev/null || true
    [[ -n "${provider_v1_log_pid:-}" ]] && kill $provider_v1_log_pid 2>/dev/null || true
    [[ -n "${provider_v2_log_pid:-}" ]] && kill $provider_v2_log_pid 2>/dev/null || true
    
    log_info "Log monitoring completed. Check /tmp/*_logs.txt for details"
}

# 生成测试报告
generate_report() {
    local report_file="/tmp/xds_test_report.txt"
    
    log_info "Generating test report..."
    
    cat > $report_file << EOF
XDS Strategy Test Report
========================
Generated at: $(date)
Namespace: $NAMESPACE

Test Environment:
- Consumer Pod: $CONSUMER_POD
- Provider V1 Pod: $PROVIDER_V1_POD
- Provider V2 Pod: $PROVIDER_V2_POD

Test Results:
EOF
    
    # 分析日志文件中的关键信息
    if [[ -f "/tmp/consumer_logs.txt" ]]; then
        echo "" >> $report_file
        echo "Consumer Log Analysis:" >> $report_file
        grep -E "(SUCCESS|WORKING|NOT WORKING|ERROR)" /tmp/consumer_logs.txt >> $report_file 2>/dev/null || echo "No test results found in consumer logs" >> $report_file
    fi
    
    log_success "Test report generated: $report_file"
    cat $report_file
}

# 主函数
main() {
    log_info "Starting XDS Strategy Tests..."
    
    # 检查kubectl连接
    if ! kubectl cluster-info &>/dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    get_pod_names
    
    # 开始监控日志
    monitor_logs 300 &
    local monitor_pid=$!
    
    # 运行测试
    test_timeout_strategy
    sleep 10
    
    test_retry_strategy
    sleep 10
    
    test_weight_routing
    sleep 10
    
    test_hash_routing
    sleep 10
    
    # 停止日志监控
    kill $monitor_pid 2>/dev/null || true
    
    # 生成报告
    generate_report
    
    log_success "All XDS strategy tests completed!"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
