# 测试一致性哈希的VirtualService
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: dubbo-hash-test
  namespace: dubbo-proxyless
spec:
  hosts:
  - dubbo-demo-xds-provider.dubbo-proxyless.svc.cluster.local
  http:
  - match:
    - headers:
        test-scenario:
          exact: "hash-test"
    timeout: 10s
    route:
    - destination:
        host: dubbo-demo-xds-provider.dubbo-proxyless.svc.cluster.local
        subset: v1
      weight: 50
    - destination:
        host: dubbo-demo-xds-provider.dubbo-proxyless.svc.cluster.local
        subset: v2
      weight: 50