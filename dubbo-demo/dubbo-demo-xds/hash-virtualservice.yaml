# 测试一致性哈希的VirtualService
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: dubbo-hash-test
  namespace: dubbo-proxyless
spec:
  hosts:
  - dubbo-demo-xds-provider.dubbo-proxyless.svc.cluster.local
  http:
  - match:
    - headers:
        test-scenario:
          exact: "hash-test"
    route:
    - destination:
        host: dubbo-demo-xds-provider.dubbo-proxyless.svc.cluster.local